#This file is for functions that will parse the entire document. Functions should recieve the full document and return only the answer to the question.
from helpers.LLM_Handler import LLM_Handler, openAIModels, geminiModels
from helpers.Document_Processing import Doc_Processor



#policy info to extract in future, per tourplan screenshots
# Supplier name
# Has Single, Twin, Double, Triple, Quad room layouts
# Age policy: What defines an infant (0-3 for example), child or adult?
# Stay rules: Stay may start on what day of the week? Stay must include what days of the week?
# Cross season policy - first rate, average rate, split rate, not allowed
# "Internet: for B2B and extranet perpose" - does the agent have internet access, is there a URL for more info, etc.
class general_document_parser:

    def __init__(self, doc, model="gpt-4o-mini"):
        """
        Initialize the document processor with the specified model.

        Args:
            doc (str): The document to check.
            model (str): The model to use for processing. Default is "gpt-4o-mini".
        """
        if not (model in openAIModels or model in geminiModels):
            raise Exception(f"Error - unknown model: {model}")
        self.llm_handler = LLM_Handler(model)
        self.document_processor = Doc_Processor()
        self.doc = doc

    def get_valid(self):
        """
        Check if the document contains accommodation information.

        Returns:
            bool: True if the document contains accommodation information, False otherwise.
        """
        prompt = """Does the following document contain accommodation information, such as rooms for booking and their prices? Return only TRUE or FALSE."""
        response = self.llm_handler.sendMessageToLLM(self.doc,prompt)
        return response.strip().upper()=="TRUE"
    
    def get_supplier_name(self, supplier_list):
        """
        Get the names of the supplier in the document and matches it to the known supplier list.

        Returns:
            supplier(str): The name of the supplier
        """
        prompt = """In the following document, a company providing travel-related services such as accommodation, car rental, or similar offerings will be mentioned. 
        Identify the name of the company or supplier and return only the core name, excluding any descriptive terms like "game reserve," "hotel," "lodge," or "resort." 

        Return the name from this list {supplier_list} that is the closest to the name you identified, return **ONLY** the name form the list.

        If no company name is found, return only the word 'NONE'"""
        response = self.llm_handler.sendMessageToLLM(self.doc,prompt)
        return [resp.strip() for resp in response.split("\n") if resp.strip()]

        # for extracted_name in extracted_names:
        #     match, score = process.extractOne(extracted_name, supplier_list)
        #     if score > 60:
        #         return match
        #return "NONE"

    def get_property_names(self):
        """
        Get the names of properties in the document.

        Returns:
            list: List of property names.
        """
        prompt = """ In the following document, identify the name(s) of the accommodation property or properties.
        A property refers to an establishment that offers overnight accommodation — such as a hotel, lodge, guest house, campsite, or similar. Each property may offer multiple types of accommodation (e.g., rooms, cottages, suites), but you must only return the **name of the overall property**, not the individual room or unit types.
        Do **not** return:
            - Names of restaurants, golf courses, spas, or other facilities within a resort
            - Sections that are general or descriptive headers (e.g., "Fancyland Accommodation")
            - Repeated mentions that are not unique property names
            - Areas or zones within a resort (e.g., “The Lakefront Area”)
        If a large resort (e.g., "Fancyland") is mentioned, but it contains individually named hotels (e.g., "The Royal Lodge", "Sunset Cabins"), then return only those **individually named** accommodation properties.
        Return **each valid property name on a new line**.
        If no property names are found, return only the word: **NONE**"""
        
        response = self.llm_handler.sendMessageToLLM(self.doc,prompt)
        resplist = response.split("\n")
        return [resp.strip() for resp in resplist if resp.strip()]
    
    def get_overarching_period(self, num_retries = 3):
        """
        Get the overarching period of validity in the document.

        Returns:
            list: List containing the start date and end date of the overarching period.
        """
        prompt = f"""In the following document which should contain accommodation information relating to South African accommodations, how long is the document/contract valid? 
        Return only the start date and then the end date on the following line. Format dates in ISO format, as follows: YYYY-MM-DD.  If you cannot find any dates, simply output NONE"""
        for attempt in range(num_retries):
            response = self.llm_handler.sendMessageToLLM(self.doc, prompt)
            resplist = response.split("\n")
            if len(resplist) >= 2 and resplist[0] and resplist[1]:
                break
        if response.upper()=="NONE" or not (len(resplist) >= 2 and resplist[0] and resplist[1]): return ["2020-01-01","2030-01-01"]
        return [resp.strip() for resp in resplist if resp.strip()]

    def get_periods(self):
        """
        Get the periods used to define seasonal price ranges in the document.

        Returns:
            list: List of periods.
        """
        prompt = f"""In the following document which should contain accommodation information, what periods are used to define seasonal price ranges?
        For instance, summer or winter, low season or high season, peak season, etc. Do not consider periods such as midweek or weekend, only seasonal periods. If none are mentioned, seek to identify the period in which the document is valid, and treat this as the seasonal period, for instance "10 Jan 2024 - 31 December 2024".
        However, prefer named periods such as Summer or Winter if they exist, rather than date ranges.
        Return only each period name on a new line."""
        response = self.llm_handler.sendMessageToLLM(self.doc,prompt)
        resplist = response.split("\n")  
        return [resp.strip() for resp in resplist if resp.strip()]
    
    def general_has_sto(self):
        """
        Check if the document contains a distinction between STO Rate and Published Rate.

        Returns:
            bool: True if the distinction exists, False otherwise.
        """
        prompt = f"""In the following document which should contain accommodation information, is there an explicit distinction between a "STO Rate" and a "Published Rate" ?
        Return TRUE if there is an explicitly mentioned STO Rate and Published rate, or FALSE if there is not."""
        response = self.llm_handler.sendMessageToLLM(self.doc,prompt)
        return response.upper()=="TRUE"
