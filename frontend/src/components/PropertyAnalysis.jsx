import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import EnhancedPdfViewer from './EnhancedPdfViewer';

function PropertyAnalysis() {
    const navigate = useNavigate();
    const location = useLocation();
    // const [documentInfo, setDocumentInfo] = React.useState(null); // Seems unused, can be removed if not needed elsewhere
    // const [propertyList, setPropertyList] = React.useState([]); // Seems unused, can be removed if not needed elsewhere
    const [pdfUrl, setPdfUrl] = React.useState(null);
    const [pdfFile, setPdfFile] = React.useState(null); // State to hold the file object
    const [selectedProperty, setSelectedProperty] = React.useState(null);
    const [propertyAnalysis, setPropertyAnalysis] = React.useState({
        includes: [],
        excludes: [],
        validation: null,
        roomTypes: { room_types: [] },
        mealTypes: { meal_types: [] },
        periods: [],
        childPolicy: null,
        levies: null
    });
    const [loading, setLoading] = React.useState(false);
    const [error, setError] = React.useState(null);
    const [selectedModel, setSelectedModel] = React.useState('gemini-2.0-flash');
    
    const [editingRoomTypes, setEditingRoomTypes] = React.useState(false);
    const [roomTypeList, setRoomTypeList] = React.useState([]);
    const [newRoomType, setNewRoomType] = React.useState('');
    
    const [editingPeriods, setEditingPeriods] = React.useState(false);
    const [periodList, setPeriodList] = React.useState([]);
    const [newPeriod, setNewPeriod] = React.useState({ name: '', start: '', end: '' });
    
    const [editingMealTypes, setEditingMealTypes] = React.useState(false);
    const [mealTypeList, setMealTypeList] = React.useState([]);
    const [newMealType, setNewMealType] = React.useState('');
    
    const [editingIncludes, setEditingIncludes] = React.useState(false);
    const [includesList, setIncludesList] = React.useState([]);
    const [newInclude, setNewInclude] = React.useState('');
    
    const [editingExcludes, setEditingExcludes] = React.useState(false);
    const [excludesList, setExcludesList] = React.useState([]);
    const [newExclude, setNewExclude] = React.useState('');
    
    const [editingChildPolicy, setEditingChildPolicy] = React.useState(false);
    const [childPolicyList, setChildPolicyList] = React.useState([]);
    const [newChildPolicy, setNewChildPolicy] = React.useState({ min: '', max: '' });
    
    const [editingLevies, setEditingLevies] = React.useState(false);
    const [leviesList, setLeviesList] = React.useState([]);
    const [newLevy, setNewLevy] = React.useState({ TYPE: '', PERCENTAGE: '', COST: '' });
    
    const [workflowState, setWorkflowState] = React.useState(null);

    // State for PDF search functionality
    const [searchText, setSearchText] = React.useState('');
    const [searchStatus, setSearchStatus] = React.useState(null); // e.g., 'searching', 'found', 'not_found'

    const modelOptions = [
        { value: 'gemini-2.0-flash', label: 'Gemini 2.0 Flash' },
        { value: 'gpt-4.1-mini', label: 'GPT-4.1 Mini' }
    ];

    const storedBaseUrl = localStorage.getItem('baseUrl');
    const baseUrl = storedBaseUrl || 'http://localhost:6060';

    if (!storedBaseUrl) {
        localStorage.setItem('baseUrl', baseUrl);
    }

    React.useEffect(() => {
        const savedWorkflowState = localStorage.getItem('workflowState');
        const receivedFile = location.state?.file;
        const savedModel = localStorage.getItem('selectedModel');

        if (savedModel) {
            console.log(`setting selected model to: ${savedModel}`);
            setSelectedModel(savedModel);
        } else {
            console.log(`No saved model. Leaving selected model as default`);
        }

        if (receivedFile) {
            setPdfFile(receivedFile); // Store the file object in state
            const fileUrl = URL.createObjectURL(receivedFile);
            setPdfUrl(fileUrl);
        } else {
            setError("PDF file not received. Please go back and try again.");
            setLoading(false);
            return;
        }

        if (savedWorkflowState) {
            const parsedState = JSON.parse(savedWorkflowState);
            setWorkflowState(parsedState);

            if (parsedState.currentPropertyIndex >= 0 && parsedState.currentPropertyIndex < parsedState.properties.length) {
                const currentProperty = parsedState.properties[parsedState.currentPropertyIndex];
                setSelectedProperty(currentProperty);
                console.log(`Workflow: Analyzing property ${parsedState.currentPropertyIndex + 1} of ${parsedState.totalProperties}: ${currentProperty}`);
                fetchPropertyAnalysis(currentProperty, parsedState);
            } else {
                setError(`Invalid property index (${parsedState.currentPropertyIndex}) in workflow state.`);
                console.error("Invalid workflow state:", parsedState);
            }
        } else {
            setError("Workflow state not found in localStorage. Please start from the home page.");
            setLoading(false);
        }
    }, [location.state]);

    const handlePropertyAnalysisBackNavigation = async () => {
        const cameFromLastStepFlag = localStorage.getItem('cameFromRoomTypeAnalysisLastStep');
        localStorage.removeItem('cameFromRoomTypeAnalysisLastStep'); // Remove flag immediately

        const currentPdfFile = pdfFile || location.state?.file; // Ensure pdfFile is available

        if (!currentPdfFile) {
            console.error("PDF file is missing. Navigating to home.");
            navigate('/'); // Navigate home if no PDF file
            return;
        }

        const savedWorkflowStateString = localStorage.getItem('workflowState');
        if (!savedWorkflowStateString) {
            console.warn("Workflow state not found. Navigating to home.");
            navigate('/', { state: { file: currentPdfFile } });
            return;
        }

        try {
            const currentWorkflowState = JSON.parse(savedWorkflowStateString);

            if (cameFromLastStepFlag === 'true' &&
                currentWorkflowState &&
                Array.isArray(currentWorkflowState.properties) &&
                typeof currentWorkflowState.currentPropertyIndex === 'number' &&
                currentWorkflowState.currentPropertyIndex > 0) {

                const previousPropertyIndex = currentWorkflowState.currentPropertyIndex - 1;
                if (previousPropertyIndex < 0 || previousPropertyIndex >= currentWorkflowState.properties.length) {
                    console.error("Previous property index out of bounds.");
                    navigate('/', { state: { file: currentPdfFile } });
                    return;
                }
                const previousPropertyName = currentWorkflowState.properties[previousPropertyIndex];
                const docFilename = currentWorkflowState.documentFilename;
                const modelToUse = currentWorkflowState.model || selectedModel; // Prioritize model from workflowState

                if (!docFilename || !previousPropertyName) {
                    console.error("Document filename or previous property name is missing from workflow state.");
                    navigate('/', { state: { file: currentPdfFile } });
                    return;
                }

                setLoading(true);
                setError(null);

                try {
                    const response = await fetch(`${baseUrl}/property-room-types`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            filename: docFilename,
                            property_name: previousPropertyName,
                            model: modelToUse
                        })
                    });

                    if (!response.ok) {
                        const errorText = await response.text();
                        throw new Error(`Failed to fetch room types for ${previousPropertyName}: ${errorText} (Status: ${response.status})`);
                    }

                    const roomTypesData = await response.json();
                    const previousPropertyRoomTypes = roomTypesData.room_types || [];

                    if (previousPropertyRoomTypes.length > 0) {
                        console.log(`Found ${previousPropertyRoomTypes.length} room types for previous property ${previousPropertyName}. Navigating to RoomTypeAnalysis.`);
                        const targetRoomTypeIndex = previousPropertyRoomTypes.length; // Go to the last room type

                        // NOTE: Fetching periods for the previous property is not done here.
                        // RoomTypeAnalysis will need to be robust or fetch its own periods if currentPropertyPeriods is not set.
                        // For now, we assume currentPropertyPeriods might be stale or RoomTypeAnalysis handles it.
                        // A more complete solution might involve fetching periods here as well or ensuring workflowState has them.
                        // For now, we are only ensuring room types and indices are set.

                        const updatedWorkflowState = {
                            ...currentWorkflowState,
                            currentPropertyIndex: previousPropertyIndex,
                            currentRoomTypeIndex: targetRoomTypeIndex,
                            currentPropertyRoomTypes: previousPropertyRoomTypes,
                            // currentPropertyPeriods: should be fetched or handled by RoomTypeAnalysis if needed for prev property
                        };
                        localStorage.setItem('workflowState', JSON.stringify(updatedWorkflowState));
                        navigate('/room-type-analysis', { state: { file: currentPdfFile } });
                    } else {
                        console.warn(`No room types found for previous property ${previousPropertyName}. Navigating to App.jsx`);
                        // Fallback: If no room types, or if we decide not to go to RoomTypeAnalysis for an empty list,
                        // we could navigate to PropertyAnalysis for that previous property instead,
                        // but that would require re-fetching its full details.
                        // For simplicity, current fallback is to App.jsx.
                        // Or, stay on PropertyAnalysis for the *previous* property by calling fetchPropertyAnalysis(previousPropertyName, updatedWorkflowState)
                        // and then navigating to self, but that's complex. Simplest is App.jsx.
                        navigate('/', { state: { file: currentPdfFile } });
                    }
                } catch (fetchError) {
                    console.error('Error during back navigation to previous property room type:', fetchError);
                    setError(`Error navigating back: ${fetchError.message}`); // Show error to user
                    // Decide on fallback navigation, e.g., stay or go to App.jsx
                    navigate('/', { state: { file: currentPdfFile } }); // Fallback on error
                } finally {
                    setLoading(false);
                }
            } else {
                // Flag not true, or it's the first property, or workflowState is invalid. Navigate to App.jsx.
                navigate('/', { state: { file: currentPdfFile } });
            }
        } catch (parseError) {
            console.error("Error parsing workflowState:", parseError);
            navigate('/', { state: { file: currentPdfFile } }); // Fallback on error
        }
    };

    const fetchPropertyAnalysis = async (property, workflowData) => {
        setLoading(true);
        setError(null);

        const currentModel = localStorage.getItem('selectedModel') || selectedModel;

        // Reset propertyAnalysis to ensure clean state before fetching
        setPropertyAnalysis({
            includes: [],
            excludes: [],
            validation: null,
            roomTypes: { room_types: [] },
            mealTypes: { meal_types: [] },
            periods: [],
            childPolicy: null,
            levies: null
        });

        try {
            const filename = workflowData.documentFilename;
            // Ensure periodsListForCheck is always an array
            const periodsListForCheck = (workflowData.periods && Array.isArray(workflowData.periods.periods)) ? workflowData.periods.periods : [];
            const startDate = workflowData.overarchingPeriod && workflowData.overarchingPeriod.start_date || null;
            const endDate = workflowData.overarchingPeriod && workflowData.overarchingPeriod.end_date || null;

            console.log('Request details:', {
                filename: filename,
                property: property,
                model: currentModel,
                periodsForCheck: periodsListForCheck,
                startDateForCheck: startDate,
                endDateForCheck: endDate
            });

            const responses = await Promise.all([
                fetch(`${baseUrl}/property-includes`, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ filename, property_name: property, model: currentModel }) }),
                fetch(`${baseUrl}/property-excludes`, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ filename, property_name: property, model: currentModel }) }),
                fetch(`${baseUrl}/validate-property`, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ filename, property_name: property, model: currentModel }) }),
                fetch(`${baseUrl}/property-room-types`, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ filename, property_name: property, model: currentModel }) }),
                fetch(`${baseUrl}/property-meal-types`, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ filename, property_name: property, model: currentModel }) }),
                fetch(`${baseUrl}/check-periods`, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ filename, property_name: property, model: currentModel, periods: periodsListForCheck, start_date: startDate, end_date: endDate }) }),
                fetch(`${baseUrl}/property-child-policy`, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ filename, property_name: property, model: currentModel }) }),
                fetch(`${baseUrl}/property-levies`, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ filename, property_name: property, model: currentModel }) })
            ]);

            for (const response of responses) {
                if (!response.ok) {
                    // Attempt to get error message from response body
                    let errorData;
                    try {
                        errorData = await response.json();
                    } catch (e) {
                        // Ignore if response body is not JSON
                    }
                    throw new Error(`Failed to fetch property analysis (status ${response.status}): ${errorData?.detail || response.statusText}`);
                }
            }

            const [
                includesData, excludesData, validateData, roomTypesData,
                mealTypesData, periodsData, childPolicyData, leviesData
            ] = await Promise.all(responses.map(res => res.json()));

            const parseCsvString = (csvString) => {
                if (typeof csvString === 'string' && csvString.trim() !== '') {
                    return csvString.split(',').map(item => item.trim());
                }
                return [];
            };
            
            setPropertyAnalysis({
                includes: parseCsvString(includesData?.includes),
                excludes: parseCsvString(excludesData?.excludes),
                validation: validateData || null,
                roomTypes: roomTypesData || { room_types: [] },
                mealTypes: mealTypesData || { meal_types: [] },
                periods: (periodsData && Array.isArray(periodsData.periods)) ? periodsData.periods : [],
                childPolicy: childPolicyData || null,
                levies: leviesData || null
            });

        } catch (err) {
            console.error('Full error:', err);
            setError(err.message);
        } finally {
            setLoading(false);
        }
    };
    
    // Synchronize local list states with propertyAnalysis
    React.useEffect(() => {
        setPeriodList(Array.isArray(propertyAnalysis?.periods) ? propertyAnalysis.periods : []);
    }, [propertyAnalysis?.periods]);

    React.useEffect(() => {
        setRoomTypeList(Array.isArray(propertyAnalysis?.roomTypes?.room_types) ? propertyAnalysis.roomTypes.room_types : []);
    }, [propertyAnalysis?.roomTypes?.room_types]);

    React.useEffect(() => {
        setMealTypeList(Array.isArray(propertyAnalysis?.mealTypes?.meal_types) ? propertyAnalysis.mealTypes.meal_types : []);
    }, [propertyAnalysis?.mealTypes?.meal_types]);

    React.useEffect(() => {
        setIncludesList(Array.isArray(propertyAnalysis?.includes) ? propertyAnalysis.includes : []);
    }, [propertyAnalysis?.includes]);

    React.useEffect(() => {
        setExcludesList(Array.isArray(propertyAnalysis?.excludes) ? propertyAnalysis.excludes : []);
    }, [propertyAnalysis?.excludes]);

    React.useEffect(() => {
        setChildPolicyList(Array.isArray(propertyAnalysis?.childPolicy?.child_policy) ? propertyAnalysis.childPolicy.child_policy : []);
    }, [propertyAnalysis?.childPolicy?.child_policy]);

    React.useEffect(() => {
        setLeviesList(Array.isArray(propertyAnalysis?.levies?.levies) ? propertyAnalysis.levies.levies : []);
    }, [propertyAnalysis?.levies?.levies]);


    // Handle room type editing
    const handleRoomTypeEdit = () => setEditingRoomTypes(true);
    const handleRoomTypeSave = () => {
        setEditingRoomTypes(false);
        setPropertyAnalysis(prevAnalysis => ({
            ...prevAnalysis,
            roomTypes: {
                ...prevAnalysis.roomTypes, // Preserve other potential properties in roomTypes
                room_types: roomTypeList
            }
        }));
    };
    const handleRoomTypeAdd = () => {
        if (newRoomType.trim() && !roomTypeList.includes(newRoomType.trim())) {
            setRoomTypeList([...roomTypeList, newRoomType.trim()]);
            setNewRoomType('');
        }
    };
    const handleRoomTypeRemove = (roomTypeToRemove) => {
        setRoomTypeList(roomTypeList.filter(roomType => roomType !== roomTypeToRemove));
    };
    const handleRoomTypeKeyPress = (e) => {
        if (e.key === 'Enter') {
            e.preventDefault();
            handleRoomTypeAdd();
        }
    };

    // Handle period editing
    const handlePeriodEdit = () => setEditingPeriods(true);
    const handlePeriodSave = () => {
        setEditingPeriods(false);
        setPropertyAnalysis(prev => ({
            ...prev,
            periods: periodList
        }));
    };
    const handlePeriodAdd = () => {
        if (
            newPeriod.name.trim() &&
            newPeriod.start && // Date inputs provide valueAsDate or value
            newPeriod.end &&
            !periodList.some(
                p =>
                    Array.isArray(p) && // Ensure p is an array
                    p[0] === newPeriod.name.trim() &&
                    p[1] === newPeriod.start &&
                    p[2] === newPeriod.end
            )
        ) {
            setPeriodList([...periodList, [newPeriod.name.trim(), newPeriod.start, newPeriod.end]]);
            setNewPeriod({ name: '', start: '', end: '' });
        }
    };
    const handlePeriodRemove = (index) => {
        setPeriodList(periodList.filter((_, i) => i !== index));
    };

    // Handle meal type editing
    const handleMealTypeEdit = () => setEditingMealTypes(true);
    const handleMealTypeSave = () => {
        setEditingMealTypes(false);
        setPropertyAnalysis(prev => ({
            ...prev,
            mealTypes: { 
                ...prev.mealTypes,
                meal_types: mealTypeList 
            }
        }));
    };
    const handleMealTypeAdd = () => {
        if (newMealType.trim() && !mealTypeList.includes(newMealType.trim())) {
            setMealTypeList([...mealTypeList, newMealType.trim()]);
            setNewMealType('');
        }
    };
    const handleMealTypeRemove = (item) => setMealTypeList(mealTypeList.filter(m => m !== item));
    const handleMealTypeKeyPress = (e) => {
        if (e.key === 'Enter') {
            e.preventDefault();
            handleMealTypeAdd();
        }
    };

    // Handle includes editing
    const handleIncludesEdit = () => setEditingIncludes(true);
    const handleIncludesSave = () => {
        setEditingIncludes(false);
        setPropertyAnalysis(prev => ({
            ...prev,
            includes: includesList
        }));
    };
    const handleIncludesAdd = () => {
        if (newInclude.trim() && !includesList.includes(newInclude.trim())) {
            setIncludesList([...includesList, newInclude.trim()]);
            setNewInclude('');
        }
    };
    const handleIncludesRemove = (item) => setIncludesList(includesList.filter(i => i !== item));
    const handleIncludesKeyPress = (e) => {
        if (e.key === 'Enter') {
            e.preventDefault();
            handleIncludesAdd();
        }
    };

    // Handle excludes editing
    const handleExcludesEdit = () => setEditingExcludes(true);
    const handleExcludesSave = () => {
        setEditingExcludes(false);
        setPropertyAnalysis(prev => ({
            ...prev,
            excludes: excludesList
        }));
    };
    const handleExcludesAdd = () => {
        if (newExclude.trim() && !excludesList.includes(newExclude.trim())) {
            setExcludesList([...excludesList, newExclude.trim()]);
            setNewExclude('');
        }
    };
    const handleExcludesRemove = (item) => setExcludesList(excludesList.filter(i => i !== item));
    const handleExcludesKeyPress = (e) => {
        if (e.key === 'Enter') {
            e.preventDefault();
            handleExcludesAdd();
        }
    };

    // Handle child policy editing
    const handleChildPolicyEdit = () => setEditingChildPolicy(true);
    const handleChildPolicySave = () => {
        setEditingChildPolicy(false);
        setPropertyAnalysis(prev => ({
            ...prev,
            childPolicy: { 
                ...prev.childPolicy,
                child_policy: childPolicyList 
            }
        }));
    };
    const handleChildPolicyAdd = () => {
        if (
            newChildPolicy.min.trim() !== '' && // Ensure min is not just spaces
            newChildPolicy.max.trim() !== '' && // Ensure max is not just spaces
            !childPolicyList.some(
                range => 
                    Array.isArray(range) && // Ensure range is an array
                    range[0] === newChildPolicy.min.trim() && 
                    range[1] === newChildPolicy.max.trim()
            )
        ) {
            setChildPolicyList([...childPolicyList, [newChildPolicy.min.trim(), newChildPolicy.max.trim()]]);
            setNewChildPolicy({ min: '', max: '' });
        }
    };
    const handleChildPolicyRemove = (index) => setChildPolicyList(childPolicyList.filter((_, i) => i !== index));

    // Handle levies editing
    const handleLeviesEdit = () => setEditingLevies(true);
    const handleLeviesSave = () => {
        setEditingLevies(false);
        setPropertyAnalysis(prev => ({
            ...prev,
            levies: { 
                ...prev.levies,
                levies: leviesList 
            }
        }));
    };
    const handleLeviesAdd = () => {
        // Ensure TYPE is not just spaces and at least one of PERCENTAGE or COST has a value
        if (newLevy.TYPE.trim() && (newLevy.PERCENTAGE.toString().trim() !== '' || newLevy.COST.toString().trim() !== '')) {
            setLeviesList([...leviesList, { ...newLevy, TYPE: newLevy.TYPE.trim() }]);
            setNewLevy({ TYPE: '', PERCENTAGE: '', COST: '' });
        }
    };
    const handleLeviesRemove = (index) => setLeviesList(leviesList.filter((_, i) => i !== index));

    // Function to map complex search terms to more effective search terms
    const mapSearchTerm = (itemText) => {
        if (!itemText || typeof itemText !== 'string') {
            return itemText;
        }

        const text = itemText.trim();

        // Handle section headers
        if (text === 'Meal Types') return 'meal';
        if (text === 'Child Policy') return 'child';
        if (text === 'Periods') return 'period';

        // Handle meal type mappings - extract key terms
        if (text.includes('B&B') || text.includes('Bed & Breakfast')) return 'breakfast';
        if (text.includes('HB') || text.includes('Half Board')) return 'half board';
        if (text.includes('FB') || text.includes('Full Board')) return 'full board';
        if (text.includes('AI') || text.includes('All-Inclusive') || text.includes('All Inclusive')) return 'all inclusive';
        if (text.includes('RO') || text.includes('Room Only')) return 'room only';
        if (text.includes('SC') || text.includes('Self Catering')) return 'self catering';

        // Handle child age range labels - convert to simple "child"
        if (text.match(/^Child Age Range \d+:?$/i)) return 'child';

        // Handle individual age numbers in child policy context
        // If it's a single digit or small number that could be an age, and we're in child policy context
        if (text.match(/^\d{1,2}$/) && parseInt(text) <= 18) {
            // For single age numbers, search for the full range if we can determine it
            // This is a simplified approach - in a real implementation, you might want to
            // track context to know which range this age belongs to
            return 'child';
        }

        // Handle age range separators and suffixes
        if (text === '-' || text === 'years') return 'child';

        // Handle complex child age range patterns - extract the full range
        const ageRangeMatch = text.match(/(\d+)\s*-\s*(\d+)\s*years?/i);
        if (ageRangeMatch) {
            return `${ageRangeMatch[1]} - ${ageRangeMatch[2]}`;
        }

        // Handle percentage labels and values
        if (text.includes('Percentage') || text.includes('%')) {
            return text; // Keep percentage searches as-is for now
        }

        // Handle cost/levy related terms
        if (text.includes('Cost') || text.includes('Levy') || text.includes('Fee')) {
            return text; // Keep cost searches as-is for now
        }

        // For very short terms that might be too broad, check if they're likely ages
        if (text.length <= 2 && text.match(/^\d+$/) && parseInt(text) <= 18) {
            return 'child'; // Single digits in this context are likely ages
        }

        // Default: return the original text
        return text;
    };

    // Handlers for PDF search functionality
    const handleItemClick = (itemText) => {
        const mappedSearchTerm = mapSearchTerm(itemText);
        setSearchText(mappedSearchTerm);
        setSearchStatus('searching'); // Indicate that viewer should start searching
    };

    const handleSearchResult = (status) => {
        setSearchStatus(status); // Update status based on search result from viewer (e.g., 'found', 'not_found')
    };

    const handleRoomTypeAnalysis = () => {
        if (!workflowState) {
            setError("Workflow state is not available. Cannot proceed.");
            return;
        }

        // Use the most up-to-date lists from their respective states
        const updatedWorkflowState = {
            ...workflowState,
            currentPropertyRoomTypes: roomTypeList, // Use roomTypeList state
            currentPropertyPeriods: periodList     // Use periodList state
        };

        try {
            localStorage.setItem('workflowState', JSON.stringify(updatedWorkflowState));
            // Use the local list states for localStorage as well
            localStorage.setItem('includes', JSON.stringify(includesList));
            localStorage.setItem('excludes', JSON.stringify(excludesList));
            localStorage.setItem('childAgeRanges', JSON.stringify(childPolicyList));
            localStorage.setItem('mealBasis', JSON.stringify(mealTypeList));

            console.log(`Workflow: Proceeding to Room Type analysis for property: ${selectedProperty}`);
            navigate('/room-type-analysis', { state: { file: pdfFile } });
        } catch (error) {
            setError("Failed to save workflow state to localStorage before navigating.");
            console.error("localStorage error:", error);
        }
    };
    
    // Helper to format date strings for display, handling invalid dates
    const formatDateString = (dateStr) => {
        if (!dateStr) return 'N/A';
        const date = new Date(dateStr);
        // Check if date is valid; date objects from invalid strings will result in "Invalid Date"
        // getTime() on an invalid date returns NaN
        if (isNaN(date.getTime())) {
            return 'Invalid Date'; 
        }
        // Adjust for timezone issues if dateStr is like "YYYY-MM-DD"
        // by creating date in UTC to avoid off-by-one day errors.
        const [year, month, day] = dateStr.split('-').map(Number);
        if (year && month && day) {
            return new Date(Date.UTC(year, month - 1, day)).toLocaleDateString(undefined, { timeZone: 'UTC' });
        }
        return date.toLocaleDateString(); // Fallback for other formats, though might have timezone issues
    };

    // Helper to render search status icons
    const renderSearchStatusIcon = (itemText) => {
        if (searchText === itemText) {
            if (searchStatus === 'searching') {
                return <span className="ml-2 animate-spin">⏳</span>; // Spinner
            } else if (searchStatus === 'found') {
                return <span className="ml-2 text-green-500">✔️</span>; // Checkmark
            } else if (searchStatus === 'not_found') {
                return <span className="ml-2 text-red-500">❌</span>; // Cross
            }
        }
        return null;
    };


    return (
        <div className="min-h-screen bg-gray-100 py-6">
            <div className="container mx-auto px-4">
                <div className="flex justify-between items-center mb-8">
                    <h1 className="text-3xl font-bold">Property Analysis</h1>
                    <button
                        onClick={handlePropertyAnalysisBackNavigation}
                        className="px-4 py-2 text-blue-500 hover:text-blue-700"
                    >
                        ← Back
                    </button>
                </div>
                
                {error && (
                    <div className="mb-8 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
                        {error}
                    </div>
                )}
                
                <div className="mb-3">
                    <label htmlFor="modelSelect" className="block text-sm font-medium text-gray-700 mb-1">Select Model:</label>
                    <select 
                        id="modelSelect" 
                        className="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                        value={selectedModel} 
                        onChange={(e) => {
                            setSelectedModel(e.target.value);
                            localStorage.setItem('selectedModel', e.target.value);
                            // Optionally re-fetch analysis if model changes and a property is selected
                            // if (selectedProperty && workflowState) {
                            //     fetchPropertyAnalysis(selectedProperty, workflowState);
                            // }
                        }}
                    >
                        {modelOptions.map(option => (
                            <option key={option.value} value={option.value}>
                                {option.label}
                            </option>
                        ))}
                    </select>
                </div>
                
                <div className="grid grid-cols-1 lg:grid-cols-5 gap-8 items-start"> {/* Changed items-stretch to items-start */}
                    <div className="lg:col-span-2 flex flex-col gap-6"> {/* Removed h-full */}
                        <div className="bg-white p-6 rounded-lg shadow-md flex-grow">
                            {selectedProperty && workflowState && (
                                <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                                    <h2 className="text-xl font-semibold text-blue-800">
                                        Analyzing Property {workflowState.currentPropertyIndex + 1} / {workflowState.totalProperties}:
                                        <span 
                                            onClick={() => handleItemClick(selectedProperty)} 
                                            className="ml-2 font-bold cursor-pointer hover:underline"
                                        >
                                            {selectedProperty}
                                        </span>
                                        {renderSearchStatusIcon(selectedProperty)}
                                    </h2>
                                </div>
                            )}
                            <h2 className="text-xl font-semibold mb-4">Analysis Results</h2>
                            
                            {!selectedProperty && !loading && (
                                <p className="text-gray-500">Select a property or start workflow to view its analysis.</p>
                            )}
                            
                            {loading && (
                                <div className="flex justify-center items-center h-40">
                                    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
                                </div>
                            )}
                            
                            {!loading && propertyAnalysis && ( // Ensure propertyAnalysis is not null before rendering sections
                                <div className="space-y-6">
                                    {/* Periods Section */}
                                    <div>
                                        <h3 onClick={() => handleItemClick('Periods')} className="text-lg font-medium text-indigo-600 mb-2 cursor-pointer hover:underline">Periods</h3>
                                        <div className="bg-indigo-50 p-4 rounded-lg">
                                            {editingPeriods ? (
                                                <React.Fragment>
                                                    <div className="space-y-2">
                                                        {periodList.map((period, index) => (
                                                            <div key={index} className="flex items-center gap-2">
                                                                <span className="flex-grow">{period[0]}: {formatDateString(period[1])} - {formatDateString(period[2])}</span>
                                                                <button
                                                                    onClick={() => handlePeriodRemove(index)}
                                                                    className="text-red-500 hover:text-red-700"
                                                                >
                                                                    ×
                                                                </button>
                                                            </div>
                                                        ))}
                                                    </div>
                                                    <div className="grid grid-cols-1 sm:grid-cols-3 gap-2 mt-2 items-end">
                                                        <input
                                                            type="text"
                                                            value={newPeriod.name}
                                                            onChange={e => setNewPeriod({ ...newPeriod, name: e.target.value })}
                                                            placeholder="Period Name"
                                                            className="px-3 py-2 border rounded-lg w-full"
                                                        />
                                                        <input
                                                            type="date"
                                                            value={newPeriod.start}
                                                            onChange={e => setNewPeriod({ ...newPeriod, start: e.target.value })}
                                                            className="px-3 py-2 border rounded-lg w-full"
                                                        />
                                                        <input
                                                            type="date"
                                                            value={newPeriod.end}
                                                            onChange={e => setNewPeriod({ ...newPeriod, end: e.target.value })}
                                                            className="px-3 py-2 border rounded-lg w-full"
                                                        />
                                                         <button
                                                            onClick={handlePeriodAdd}
                                                            className="sm:col-start-3 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 h-full"
                                                        >
                                                            Add
                                                        </button>
                                                    </div>
                                                    <div className="flex justify-end gap-2 mt-2">
                                                        <button onClick={() => { setEditingPeriods(false); setNewPeriod({ name: '', start: '', end: '' }); setPeriodList(Array.isArray(propertyAnalysis?.periods) ? propertyAnalysis.periods : []); }} className="px-4 py-2 text-gray-600 hover:text-gray-800">Cancel</button>
                                                        <button onClick={handlePeriodSave} className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600">Save</button>
                                                    </div>
                                                </React.Fragment>
                                            ) : (
                                                <React.Fragment>
                                                    {periodList && periodList.length > 0 ? (
                                                        <div>
                                                            <div className="space-y-4">
                                                                <div className="grid grid-cols-3 gap-4 font-semibold text-indigo-700">
                                                                    <div onClick={() => handleItemClick('Period Name')} className="cursor-pointer hover:underline">Period Name</div>
                                                                    <div onClick={() => handleItemClick('Start Date')} className="cursor-pointer hover:underline">Start Date</div>
                                                                    <div onClick={() => handleItemClick('End Date')} className="cursor-pointer hover:underline">End Date</div>
                                                                </div>
                                                                {periodList.map((period, index) => {
                                                                    const periodName = Array.isArray(period) ? period[0] : 'N/A';
                                                                    const startDate = Array.isArray(period) ? formatDateString(period[1]) : 'N/A';
                                                                    const endDate = Array.isArray(period) ? formatDateString(period[2]) : 'N/A';
                                                                    return (
                                                                        <div key={index} className="grid grid-cols-3 gap-4 items-center">
                                                                            <div>
                                                                                <span onClick={() => handleItemClick(periodName)} className="cursor-pointer hover:underline">
                                                                                    {periodName}
                                                                                </span>
                                                                                {renderSearchStatusIcon(periodName)}
                                                                            </div>
                                                                            <div>
                                                                                <span onClick={() => handleItemClick(startDate)} className="cursor-pointer hover:underline">
                                                                                    {startDate}
                                                                                </span>
                                                                                {renderSearchStatusIcon(startDate)}
                                                                            </div>
                                                                            <div>
                                                                                <span onClick={() => handleItemClick(endDate)} className="cursor-pointer hover:underline">
                                                                                    {endDate}
                                                                                </span>
                                                                                {renderSearchStatusIcon(endDate)}
                                                                            </div>
                                                                        </div>
                                                                    );
                                                                })}
                                                            </div>
                                                        </div>
                                                    ) : ( <p className="text-gray-500">No periods found</p> )}
                                                    <div className="flex justify-end mt-4"> <button onClick={handlePeriodEdit} className="px-4 py-2 text-blue-500 hover:text-blue-700">Edit Periods</button> </div>
                                                </React.Fragment>
                                            )}
                                        </div>
                                    </div>

                                    {/* Room Types Section */}
                                    <div>
                                        <h3 onClick={() => handleItemClick('Room Types')} className="text-lg font-medium text-purple-600 mb-2 cursor-pointer hover:underline">Room Types</h3>
                                        <div className="bg-purple-50 p-4 rounded-lg">
                                            {editingRoomTypes ? (
                                                <React.Fragment>
                                                    <div className="space-y-2">
                                                        {roomTypeList.map((roomType, index) => (
                                                            <div key={index} className="flex items-center gap-2">
                                                                <span className="flex-grow">{roomType}</span>
                                                                <button onClick={() => handleRoomTypeRemove(roomType)} className="text-red-500 hover:text-red-700">×</button>
                                                            </div>
                                                        ))}
                                                    </div>
                                                    <div className="flex gap-2 mt-2">
                                                        <input type="text" value={newRoomType} onChange={(e) => setNewRoomType(e.target.value)} onKeyPress={handleRoomTypeKeyPress} placeholder="Add new room type" className="flex-grow px-3 py-2 border rounded-lg" />
                                                        <button onClick={handleRoomTypeAdd} className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">Add</button>
                                                    </div>
                                                    <div className="flex justify-end gap-2 mt-2">
                                                        <button onClick={() => {setEditingRoomTypes(false); setNewRoomType(''); setRoomTypeList(Array.isArray(propertyAnalysis?.roomTypes?.room_types) ? propertyAnalysis.roomTypes.room_types : []);}} className="px-4 py-2 text-gray-600 hover:text-gray-800">Cancel</button>
                                                        <button onClick={handleRoomTypeSave} className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600">Save</button>
                                                    </div>
                                                </React.Fragment>
                                            ) : (
                                                <React.Fragment>
                                                    {roomTypeList && roomTypeList.length > 0 ? (
                                                        <div>
                                                            <ul className="list-disc list-inside space-y-1">
                                                                {roomTypeList.map((roomType, index) => (
                                                                    <li key={index} className="flex items-center">
                                                                        <span onClick={() => handleItemClick(roomType)} className="cursor-pointer hover:underline">
                                                                            {roomType}
                                                                        </span>
                                                                        {renderSearchStatusIcon(roomType)}
                                                                    </li>
                                                                ))}
                                                            </ul>
                                                        </div>
                                                    ) : ( <p className="text-gray-500">No room types found</p> )}
                                                    <div className="flex justify-end mt-4"> <button onClick={handleRoomTypeEdit} className="px-4 py-2 text-blue-500 hover:text-blue-700">Edit Room Types</button> </div>
                                                </React.Fragment>
                                            )}
                                        </div>
                                    </div>

                                    {/* Meal Types Section */}
                                    <div>
                                        <h3 onClick={() => handleItemClick('Meal Types')} className="text-lg font-medium text-orange-600 mb-2 cursor-pointer hover:underline">Meal Types</h3>
                                        <div className="bg-orange-50 p-4 rounded-lg">
                                            {editingMealTypes ? (
                                                <React.Fragment>
                                                    <div className="space-y-2">
                                                        {mealTypeList.map((mealType, index) => (
                                                            <div key={index} className="flex items-center gap-2">
                                                                <span className="flex-grow">{mealType}</span>
                                                                <button onClick={() => handleMealTypeRemove(mealType)} className="text-red-500 hover:text-red-700">×</button>
                                                            </div>
                                                        ))}
                                                    </div>
                                                    <div className="flex gap-2 mt-2">
                                                        <input type="text" value={newMealType} onChange={e => setNewMealType(e.target.value)} onKeyPress={handleMealTypeKeyPress} placeholder="Add new meal type" className="flex-grow px-3 py-2 border rounded-lg" />
                                                        <button onClick={handleMealTypeAdd} className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">Add</button>
                                                    </div>
                                                    <div className="flex justify-end gap-2 mt-2">
                                                        <button onClick={() => {setEditingMealTypes(false); setNewMealType(''); setMealTypeList(Array.isArray(propertyAnalysis?.mealTypes?.meal_types) ? propertyAnalysis.mealTypes.meal_types : []);}} className="px-4 py-2 text-gray-600 hover:text-gray-800">Cancel</button>
                                                        <button onClick={handleMealTypeSave} className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600">Save</button>
                                                    </div>
                                                </React.Fragment>
                                            ) : (
                                                <React.Fragment>
                                                    {mealTypeList && mealTypeList.length > 0 ? (
                                                        <div>
                                                            <ul className="list-disc list-inside space-y-1">
                                                                {mealTypeList.map((mealType, index) => (
                                                                    <li key={index} className="flex items-center">
                                                                        <span onClick={() => handleItemClick(mealType)} className="cursor-pointer hover:underline">
                                                                            {mealType}
                                                                        </span>
                                                                        {renderSearchStatusIcon(mealType)}
                                                                    </li>
                                                                ))}
                                                            </ul>
                                                        </div>
                                                    ) : ( <p className="text-gray-500">No meal types found</p> )}
                                                    <div className="flex justify-end mt-4"> <button onClick={handleMealTypeEdit} className="px-4 py-2 text-blue-500 hover:text-blue-700">Edit Meal Types</button> </div>
                                                </React.Fragment>
                                            )}
                                        </div>
                                    </div>

                                    {/* Child Policy Section */}
                                    <div>
                                        <h3 onClick={() => handleItemClick('Child Policy')} className="text-lg font-medium text-cyan-600 mb-2 cursor-pointer hover:underline">Child Policy</h3>
                                        <div className="bg-cyan-50 p-4 rounded-lg">
                                            {editingChildPolicy ? (
                                                <React.Fragment>
                                                    <div className="space-y-2">
                                                        {childPolicyList.map((range, index) => (
                                                            <div key={index} className="flex items-center gap-2">
                                                                <span className="flex-grow">Child Age Range {index + 1}: {range[0]} - {range[1]} years</span>
                                                                <button onClick={() => handleChildPolicyRemove(index)} className="text-red-500 hover:text-red-700">×</button>
                                                            </div>
                                                        ))}
                                                    </div>
                                                    <div className="flex gap-2 mt-2">
                                                        <input type="number" value={newChildPolicy.min} onChange={e => setNewChildPolicy({ ...newChildPolicy, min: e.target.value })} placeholder="Min Age" className="w-1/3 px-3 py-2 border rounded-lg" />
                                                        <input type="number" value={newChildPolicy.max} onChange={e => setNewChildPolicy({ ...newChildPolicy, max: e.target.value })} placeholder="Max Age" className="w-1/3 px-3 py-2 border rounded-lg" />
                                                        <button onClick={handleChildPolicyAdd} className="w-1/3 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">Add</button>
                                                    </div>
                                                    <div className="flex justify-end gap-2 mt-2">
                                                        <button onClick={() => {setEditingChildPolicy(false); setNewChildPolicy({min:'', max:''}); setChildPolicyList(Array.isArray(propertyAnalysis?.childPolicy?.child_policy) ? propertyAnalysis.childPolicy.child_policy : []);}} className="px-4 py-2 text-gray-600 hover:text-gray-800">Cancel</button>
                                                        <button onClick={handleChildPolicySave} className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600">Save</button>
                                                    </div>
                                                </React.Fragment>
                                            ) : (
                                                <React.Fragment>
                                                    {childPolicyList && childPolicyList.length > 0 ? (
                                                        <div>
                                                            <div className="space-y-2">
                                                                {childPolicyList.map((range, index) => {
                                                                    const rangeLabel = `Child Age Range ${index + 1}:`;
                                                                    const minAge = Array.isArray(range) ? range[0].toString() : 'N/A';
                                                                    const maxAge = Array.isArray(range) ? range[1].toString() : 'N/A';
                                                                    const separator = '-';
                                                                    const yearsSuffix = 'years';

                                                                    return (
                                                                        <p key={index} className="flex items-center flex-wrap">
                                                                            <strong onClick={() => handleItemClick(rangeLabel)} className="cursor-pointer hover:underline mr-1">{rangeLabel}</strong>
                                                                            {renderSearchStatusIcon(rangeLabel)}
                                                                            <span onClick={() => handleItemClick(minAge)} className="cursor-pointer hover:underline ml-1 mr-1">{minAge}</span>
                                                                            {renderSearchStatusIcon(minAge)}
                                                                            <span onClick={() => handleItemClick(separator)} className="cursor-pointer hover:underline ml-1 mr-1">{separator}</span>
                                                                            {renderSearchStatusIcon(separator)}
                                                                            <span onClick={() => handleItemClick(maxAge)} className="cursor-pointer hover:underline ml-1 mr-1">{maxAge}</span>
                                                                            {renderSearchStatusIcon(maxAge)}
                                                                            <span onClick={() => handleItemClick(yearsSuffix)} className="cursor-pointer hover:underline ml-1">{yearsSuffix}</span>
                                                                            {renderSearchStatusIcon(yearsSuffix)}
                                                                        </p>
                                                                    );
                                                                })}
                                                            </div>
                                                        </div>
                                                    ) : ( <p className="text-gray-500">No specific child age ranges defined.</p> )}
                                                    <div className="flex justify-end mt-4"> <button onClick={handleChildPolicyEdit} className="px-4 py-2 text-blue-500 hover:text-blue-700">Edit Child Policy</button> </div>
                                                </React.Fragment>
                                            )}
                                        </div>
                                    </div>

                                    {/* Levies Section */}
                                    <div>
                                        <h3 onClick={() => handleItemClick('Levies')} className="text-lg font-medium text-purple-600 mb-2 cursor-pointer hover:underline">Levies</h3>
                                        <div className="bg-purple-50 p-4 rounded-lg">
                                            {editingLevies ? (
                                                <React.Fragment>
                                                    <div className="space-y-3">
                                                        {leviesList.map((levy, index) => (
                                                            <div key={index} className="flex items-center gap-2 border-b border-purple-200 pb-2 last:border-b-0">
                                                                <span className="flex-grow">
                                                                    <strong>Type:</strong> {levy.TYPE || 'N/A'} | <strong>%:</strong> {levy.PERCENTAGE?.toString().trim() !== '' ? `${levy.PERCENTAGE}%` : 'N/A'} | <strong>Cost:</strong> {levy.COST?.toString().trim() !== '' ? levy.COST : 'N/A'}
                                                                </span>
                                                                <button onClick={() => handleLeviesRemove(index)} className="text-red-500 hover:text-red-700">×</button>
                                                            </div>
                                                        ))}
                                                    </div>
                                                    <div className="grid grid-cols-1 sm:grid-cols-4 gap-2 mt-2 items-end">
                                                        <input type="text" value={newLevy.TYPE} onChange={e => setNewLevy({ ...newLevy, TYPE: e.target.value })} placeholder="Type" className="px-3 py-2 border rounded-lg w-full" />
                                                        <input type="number" value={newLevy.PERCENTAGE} onChange={e => setNewLevy({ ...newLevy, PERCENTAGE: e.target.value })} placeholder="Percentage" className="px-3 py-2 border rounded-lg w-full" />
                                                        <input type="number" value={newLevy.COST} onChange={e => setNewLevy({ ...newLevy, COST: e.target.value })} placeholder="Cost" className="px-3 py-2 border rounded-lg w-full" />
                                                        <button onClick={handleLeviesAdd} className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 h-full">Add</button>
                                                    </div>
                                                    <div className="flex justify-end gap-2 mt-2">
                                                        <button onClick={() => {setEditingLevies(false); setNewLevy({TYPE:'', PERCENTAGE:'', COST:''}); setLeviesList(Array.isArray(propertyAnalysis?.levies?.levies) ? propertyAnalysis.levies.levies : []);}} className="px-4 py-2 text-gray-600 hover:text-gray-800">Cancel</button>
                                                        <button onClick={handleLeviesSave} className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600">Save</button>
                                                    </div>
                                                </React.Fragment>
                                            ) : (
                                                <React.Fragment>
                                                    {leviesList && leviesList.length > 0 ? (
                                                        <div>
                                                            <div className="space-y-3">
                                                                {leviesList.map((levy, index) => {
                                                                    const typeLabel = "Type:";
                                                                    const typeValue = levy.TYPE || 'N/A';
                                                                    const percentageLabel = "Percentage:";
                                                                    const percentageValue = levy.PERCENTAGE?.toString().trim() !== '' ? `${levy.PERCENTAGE}%` : 'N/A';
                                                                    const costLabel = "Cost:";
                                                                    const costValue = levy.COST?.toString().trim() !== '' ? levy.COST.toString() : 'N/A';

                                                                    return (
                                                                        <div key={index} className="border-b border-purple-200 pb-2 last:border-b-0">
                                                                            <p className="flex items-center flex-wrap">
                                                                                <strong onClick={() => handleItemClick(typeLabel)} className="cursor-pointer hover:underline mr-1">{typeLabel}</strong>
                                                                                {renderSearchStatusIcon(typeLabel)}
                                                                                <span onClick={() => handleItemClick(typeValue)} className="cursor-pointer hover:underline ml-1">{typeValue}</span>
                                                                                {renderSearchStatusIcon(typeValue)}
                                                                            </p>
                                                                            <p className="flex items-center flex-wrap">
                                                                                <strong onClick={() => handleItemClick(percentageLabel)} className="cursor-pointer hover:underline mr-1">{percentageLabel}</strong>
                                                                                {renderSearchStatusIcon(percentageLabel)}
                                                                                <span onClick={() => handleItemClick(percentageValue)} className="cursor-pointer hover:underline ml-1">{percentageValue}</span>
                                                                                {renderSearchStatusIcon(percentageValue)}
                                                                            </p>
                                                                            <p className="flex items-center flex-wrap">
                                                                                <strong onClick={() => handleItemClick(costLabel)} className="cursor-pointer hover:underline mr-1">{costLabel}</strong>
                                                                                {renderSearchStatusIcon(costLabel)}
                                                                                <span onClick={() => handleItemClick(costValue)} className="cursor-pointer hover:underline ml-1">{costValue}</span>
                                                                                {renderSearchStatusIcon(costValue)}
                                                                            </p>
                                                                        </div>
                                                                    );
                                                                })}
                                                            </div>
                                                        </div>
                                                    ) : ( <p className="text-gray-500">No levies found for this property.</p> )}
                                                    <div className="flex justify-end mt-4"> <button onClick={handleLeviesEdit} className="px-4 py-2 text-blue-500 hover:text-blue-700">Edit Levies</button> </div>
                                                </React.Fragment>
                                            )}
                                        </div>
                                    </div>
                                    
                                    {/* Includes Section */}
                                    <div>
                                        <h3 className="text-lg font-medium text-green-600 mb-2">Includes</h3>
                                        <div className="bg-green-50 p-4 rounded-lg">
                                            {editingIncludes ? (
                                                <React.Fragment>
                                                    <div className="space-y-2">
                                                        {includesList.map((item, index) => (
                                                            <div key={index} className="flex items-center gap-2">
                                                                <span className="flex-grow">{item}</span>
                                                                <button onClick={() => handleIncludesRemove(item)} className="text-red-500 hover:text-red-700">×</button>
                                                            </div>
                                                        ))}
                                                    </div>
                                                    <div className="flex gap-2 mt-2">
                                                        <input type="text" value={newInclude} onChange={e => setNewInclude(e.target.value)} onKeyPress={handleIncludesKeyPress} placeholder="Add new include" className="flex-grow px-3 py-2 border rounded-lg" />
                                                        <button onClick={handleIncludesAdd} className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">Add</button>
                                                    </div>
                                                    <div className="flex justify-end gap-2 mt-2">
                                                        <button onClick={() => {setEditingIncludes(false); setNewInclude(''); setIncludesList(Array.isArray(propertyAnalysis?.includes) ? propertyAnalysis.includes : []);}} className="px-4 py-2 text-gray-600 hover:text-gray-800">Cancel</button>
                                                        <button onClick={handleIncludesSave} className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600">Save</button>
                                                    </div>
                                                </React.Fragment>
                                            ) : (
                                                <React.Fragment>
                                                    {includesList && includesList.length > 0 ? (
                                                        <div>
                                                            <ul className="list-disc list-inside space-y-1">
                                                                {includesList.map((item, index) => (
                                                                    <li key={index} className="flex items-center">
                                                                        <span onClick={() => handleItemClick(item)} className="cursor-pointer hover:underline">
                                                                            {item}
                                                                        </span>
                                                                        {renderSearchStatusIcon(item)}
                                                                    </li>
                                                                ))}
                                                            </ul>
                                                        </div>
                                                    ) : ( <p className="text-gray-500">No includes found</p> )}
                                                    <div className="flex justify-end mt-4"> <button onClick={handleIncludesEdit} className="px-4 py-2 text-blue-500 hover:text-blue-700">Edit Includes</button> </div>
                                                </React.Fragment>
                                            )}
                                        </div>
                                    </div>

                                    {/* Excludes Section */}
                                    <div>
                                        <h3 className="text-lg font-medium text-red-600 mb-2">Excludes</h3>
                                        <div className="bg-red-50 p-4 rounded-lg">
                                            {editingExcludes ? (
                                                <React.Fragment>
                                                    <div className="space-y-2">
                                                        {excludesList.map((item, index) => (
                                                            <div key={index} className="flex items-center gap-2">
                                                                <span className="flex-grow">{item}</span>
                                                                <button onClick={() => handleExcludesRemove(item)} className="text-red-500 hover:text-red-700">×</button>
                                                            </div>
                                                        ))}
                                                    </div>
                                                    <div className="flex gap-2 mt-2">
                                                        <input type="text" value={newExclude} onChange={e => setNewExclude(e.target.value)} onKeyPress={handleExcludesKeyPress} placeholder="Add new exclude" className="flex-grow px-3 py-2 border rounded-lg" />
                                                        <button onClick={handleExcludesAdd} className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">Add</button>
                                                    </div>
                                                    <div className="flex justify-end gap-2 mt-2">
                                                        <button onClick={() => {setEditingExcludes(false); setNewExclude(''); setExcludesList(Array.isArray(propertyAnalysis?.excludes) ? propertyAnalysis.excludes : []);}} className="px-4 py-2 text-gray-600 hover:text-gray-800">Cancel</button>
                                                        <button onClick={handleExcludesSave} className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600">Save</button>
                                                    </div>
                                                </React.Fragment>
                                            ) : (
                                                <React.Fragment>
                                                    {(excludesList && excludesList.length > 0) ? (
                                                        <ul className="list-disc list-inside space-y-1">
                                                            {excludesList.map((item, index) => (
                                                                <li key={index} className="flex items-center">
                                                                    <span onClick={() => handleItemClick(item)} className="cursor-pointer hover:underline">
                                                                        {item}
                                                                    </span>
                                                                    {renderSearchStatusIcon(item)}
                                                                </li>
                                                            ))}
                                                        </ul>
                                                    ) : (
                                                        <p className="text-gray-500">No excludes found.</p>
                                                    )}
                                                    <div className="flex justify-end mt-4">
                                                        <button onClick={handleExcludesEdit} className="px-4 py-2 text-blue-500 hover:text-blue-700">Edit Excludes</button>
                                                    </div>
                                                </React.Fragment>
                                            )}
                                        </div>
                                    </div>

                                    <div className="mt-4">
                                        <button
                                            onClick={handleRoomTypeAnalysis}
                                            className="w-full px-4 py-3 bg-green-500 text-white font-semibold rounded-lg shadow-md hover:bg-green-600"
                                            disabled={loading} // Disable button while loading
                                        >
                                            Process Room Types
                                        </button>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                    
                    <div className="lg:col-span-3 bg-white p-6 rounded-lg shadow-md sticky top-6 max-h-[calc(100vh-3rem)] overflow-y-auto"> {/* Make PDF viewer sticky */}
                        <h2 className="text-xl font-semibold mb-4">PDF Viewer</h2>
                        {pdfUrl ? <EnhancedPdfViewer url={pdfUrl} searchText={searchText} searchStatus={searchStatus} onSearchResult={handleSearchResult} /> : <p>No PDF loaded.</p>}
                    </div>
                </div>
            </div>
        </div>
    );
}

export default PropertyAnalysis;
