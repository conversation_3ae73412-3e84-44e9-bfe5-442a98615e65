import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import EnhancedPdfViewer from './EnhancedPdfViewer';

function RoomTypeAnalysis() {
    const navigate = useNavigate();
    const location = useLocation();
    const [documentInfo, setDocumentInfo] = React.useState(null);
    const [roomTypeList, setRoomTypeList] = React.useState([]);
    const [pdfUrl, setPdfUrl] = React.useState(null);
    const [pdfFile, setPdfFile] = React.useState(null);
    const [selectedRoomType, setSelectedRoomType] = React.useState(null);
    const [roomTypeAnalysis, setRoomTypeAnalysis] = React.useState(null);
    const [editableMinNightStay, setEditableMinNightStay] = React.useState(''); // New state for editable min night stay
    const [editableCancellationPolicies, setEditableCancellationPolicies] = React.useState([{ CANCELLATION_FEE: '', START_DAY: '' }]); // New state for editable cancellation policies
    const [editablePayStayOffers, setEditablePayStayOffers] = React.useState([{ PAY_STAY_DAYS: '', PAY_STAY_FREE_NIGHTS: '' }]); // New state for editable pay stay offers
    const [editableMealType, setEditableMealType] = React.useState(''); // New state for editable meal type
    const [editableRoomCapacity, setEditableRoomCapacity] = React.useState({ BASE_CAPACITY: '', TOTAL_CAPACITY: '', MAX_ADULTS: '', MAX_CHILDREN: '' }); // New state for editable room capacity
    const [editableRoomRates, setEditableRoomRates] = React.useState([]); // New state for editable room rates
    // Store the initial groupedPeriods to reconstruct initialRates on cancel if needed
    const [initialGroupedPeriods, setInitialGroupedPeriods] = React.useState([]);
    const [searchText, setSearchText] = React.useState('');
    const [searchStatus, setSearchStatus] = React.useState(null);
    const [editingMinNightStay, setEditingMinNightStay] = React.useState(false);
    const [editingCancellationPolicy, setEditingCancellationPolicy] = React.useState(false);
    const [editingPayStay, setEditingPayStay] = React.useState(false);
    const [editingMealType, setEditingMealType] = React.useState(false);
    const [editingCapacity, setEditingCapacity] = React.useState(false);
    const [editingRates, setEditingRates] = React.useState(false);
    const [loading, setLoading] = React.useState(false);
    const [error, setError] = React.useState(null);
    const [selectedModel, setSelectedModel] = React.useState('gemini-2.0-flash');
    const [selectedProperty, setSelectedProperty] = React.useState(null);
    const [periods, setPeriods] = React.useState([]);
    const [groupedPeriods, setGroupedPeriods] = React.useState([]);
    const [workflowState, setWorkflowState] = React.useState(null); // Holds the loaded workflow object
    const [currentRoomTypeIndex, setCurrentRoomTypeIndex] = React.useState(0); // Index for step-by-step
    const modelOptions = [
        { value: 'gemini-2.0-flash', label: 'Gemini 2.0 Flash' },
        { value: 'gpt-4.1-mini', label: 'GPT-4.1 Mini' }
    ];

    const storedBaseUrl = localStorage.getItem('baseUrl');
    const baseUrl = storedBaseUrl || 'http://localhost:6060';
    const csvFileID = localStorage.getItem('csvFileId'); // Retrieve file ID from localStorage
    console.log("csvFileID",csvFileID)

    // Save baseUrl to localStorage if not already stored
    if (!storedBaseUrl) {
        localStorage.setItem('baseUrl', baseUrl);
    }

    // Load data from localStorage on component mount
    React.useEffect(() => {
        const savedWorkflowState = localStorage.getItem('workflowState');
        const receivedFile = location.state?.file;
        const savedModel = localStorage.getItem('selectedModel');

        if (savedModel) setSelectedModel(savedModel);
        
        if (receivedFile) {
            setPdfFile(receivedFile);
            const fileUrl = URL.createObjectURL(receivedFile);
            setPdfUrl(fileUrl);
        } else {
            setError("PDF file not received. Please go back and try again.");
            setLoading(false);
            return;
        }

        if (savedWorkflowState) {
            const parsedState = JSON.parse(savedWorkflowState);
            setWorkflowState(parsedState);
            
            console.log("current property index", parsedState.currentPropertyIndex)
            console.log("total properties:", parsedState.properties.length)
            // Set the current property and room types from workflow state
            if (parsedState.currentPropertyIndex >= 0 && parsedState.currentPropertyIndex < parsedState.properties.length) {
                // Extract relevant info
    
              
        
                const currentProperty = parsedState.properties[parsedState.currentPropertyIndex];
                setSelectedProperty(currentProperty);
                const rooms = parsedState.currentPropertyRoomTypes || []; // Get room list for this property
                setRoomTypeList(rooms);
                const propertyPeriods = parsedState.currentPropertyPeriods
                console.log("new periods", parsedState.currentPropertyPeriods)          

                // Set periods from workflow state
                if (propertyPeriods) {
                    
                    setPeriods(propertyPeriods);
                    
                    // Group periods by name
                    const grouped = propertyPeriods.reduce((acc, period) => {
                        const name = period[0];
                        console.log("acc:", acc)
                        console.log("name", name)
                        if (!acc[name]) {
                            acc[name] = {
                                name,
                                dateRanges: []
                            };
                        }
                        acc[name].dateRanges.push({
                            start: period[1],
                            end: period[2]
                        });
                        return acc;
                    }, {});
                    
                    setGroupedPeriods(Object.values(grouped));
                    setInitialGroupedPeriods(Object.values(grouped)); // Store initial grouped periods
                    // Check if room list is valid and trigger analysis for the first room type
                    if (rooms.length > 0 && currentRoomTypeIndex < rooms.length) {
                        const currentRoom = rooms[currentRoomTypeIndex];
                        setSelectedRoomType(currentRoom);
                        console.log(`Workflow: Analyzing Room Type ${currentRoomTypeIndex + 1} / ${rooms.length}: ${currentRoom} for Property: ${currentProperty}`);
                        fetchRoomTypeAnalysis(currentRoom, currentProperty, parsedState.documentFilename, propertyPeriods, Object.values(grouped)); // Pass necessary info
                    } else if (rooms.length === 0) {
                        console.log(`Workflow: No room types found for property ${currentProperty}. Proceeding to next step.`);
                        // If no rooms, immediately trigger the logic to move to the next property/finish
                        handleProceed(); // Call handleProceed to move on
                    } else {
                        setError(`Invalid initial room type index (${currentRoomTypeIndex}) for property ${currentProperty}.`);
                    }

                }
            }
            else
            {
                console.log("Invalid property index")
            }
        } else {
            setError("Workflow state not found in localStorage. Please start from the property analysis page.");
            setLoading(false);
        }

        // Cleanup function remains similar
        return () => {
            // No need to revoke if using Data URL
        };
    }, []);

        const handleBackNavigation = () => {
        const savedWorkflowStateString = localStorage.getItem('workflowState');
        const currentPdfFile = pdfFile || location.state?.file; // Use state first, then location state

        if (!currentPdfFile) {
            console.error("pdfFile is not available. Navigating to /property-analysis as a fallback.");
            navigate('/property-analysis'); // True fallback
            return;
        }

        if (!savedWorkflowStateString) {
            console.warn("Workflow state not found in localStorage. Navigating to /property-analysis.");
            navigate('/property-analysis', { state: { file: currentPdfFile } });
            return;
        }

        try {
            const parsedWorkflowState = JSON.parse(savedWorkflowStateString);

            if (!parsedWorkflowState ||
                typeof currentRoomTypeIndex !== 'number' ||
                !Array.isArray(parsedWorkflowState.currentPropertyRoomTypes)
            ) {
                console.warn("Essential data missing or malformed in workflowState from localStorage. Navigating to /property-analysis.");
                console.log(parsedWorkflowState)
                navigate('/property-analysis', { state: { file: currentPdfFile } });
                return;
            }

            const currentIdx = currentRoomTypeIndex;
            const currentRoomTypes = parsedWorkflowState.currentPropertyRoomTypes;

            if (currentIdx > 0) {
                const newRoomTypeIndex = currentIdx - 1;

                if (newRoomTypeIndex < 0 || newRoomTypeIndex >= currentRoomTypes.length) {
                    console.error("New room type index is out of bounds. Navigating to /property-analysis.");
                    navigate('/property-analysis', { state: { file: currentPdfFile } });
                    return;
                }
                const newSelectedRoomType = currentRoomTypes[newRoomTypeIndex];

                const updatedWorkflowStateForStorage = {
                    ...parsedWorkflowState,
                    currentRoomTypeIndex: newRoomTypeIndex,
                };
                localStorage.setItem('workflowState', JSON.stringify(updatedWorkflowStateForStorage));

                setCurrentRoomTypeIndex(newRoomTypeIndex);
                setSelectedRoomType(newSelectedRoomType);
                setRoomTypeAnalysis(null);
                setError(null);

                // Ensure component's state variables for fetch are valid
                if (!selectedProperty || !workflowState || !workflowState.documentFilename || !periods || !groupedPeriods) {
                     console.error("Required data for fetchRoomTypeAnalysis (selectedProperty, documentFilename, periods, groupedPeriods) is missing from component state. Navigating to /property-analysis.");
                     navigate('/property-analysis', { state: { file: currentPdfFile } });
                     return;
                }

                fetchRoomTypeAnalysis(
                    newSelectedRoomType,
                    selectedProperty,
                    workflowState.documentFilename,
                    periods,
                    groupedPeriods
                );

            } else {
                // First room type (index 0), navigate to PropertyAnalysis
                navigate('/property-analysis', { state: { file: currentPdfFile } });
            }
        } catch (e) {
            console.error("Error parsing workflowState or during back navigation logic:", e);
            navigate('/property-analysis', { state: { file: currentPdfFile } });
        }
    };

    const fetchRoomTypeAnalysis = async (roomType, propertyName, filename, periodsData, groupedPeriodsData) => {
        setLoading(true);
        setError(null);
       
        // Initialize empty analysis state
        setRoomTypeAnalysis({
            minNightStay: null,
            totalCapacity: null,
            cancellationPolicy: null,
            payStay: null,
            mealTypeData: null,
            roomCapacityData: null,
            rates: []
        });

        try {
            
            const currentModel = localStorage.getItem('selectedModel') || selectedModel;

            // Log the request details
            console.log('Request details:', {
                filename: filename,
                property: propertyName,
                room_type: roomType,
                model: currentModel
            });


            const meal_types_available = localStorage.getItem("mealBasis") || "Unknown";
            // Fetch min night stay, cancellation policy and pay-stay information in parallel
            const [minNightStayResponse, cancellationPolicyResponse, payStayResponse, mealTypeResponse, roomCapacityResponse] = await Promise.all([
                fetch(`${baseUrl}/room-min-night-stay`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ 
                        filename: filename,
                        property_name: propertyName,
                        room_type: roomType,
                        model: currentModel
                    }),
                }),
                fetch(`${baseUrl}/room-cancellation-policy`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ 
                        filename: filename,
                        property_name: propertyName,
                        room_type: roomType,
                        model: currentModel
                    }),
                }),
                fetch(`${baseUrl}/room-pay-stays`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ 
                        filename: filename,
                        property_name: propertyName,
                        room_type: roomType,
                        model: currentModel
                    }),
                }),
                fetch(`${baseUrl}/room-meal-type`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ 
                        filename: filename,
                        property_name: propertyName,
                        room_type: roomType,
                        model: currentModel,
                        meal_types: meal_types_available
                    }),
                }),

                fetch(`${baseUrl}/room-capacity`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ 
                        filename: filename,
                        property_name: propertyName,
                        room_type: roomType,
                        model: currentModel
                    }),
                })

            ]);

            if (!minNightStayResponse.ok) {
                const minNightStayError = await minNightStayResponse.text();
                console.error('Error details:', {
                    minNightStay: minNightStayError
                });
                throw new Error(`Failed to fetch room type analysis. Min Night Stay: ${minNightStayResponse.status}`);
            }

            if (!cancellationPolicyResponse.ok) {
                const cancellationPolicyError = await cancellationPolicyResponse.text();
                console.error('Error details:', {
                    cancellationPolicy: cancellationPolicyError
                });
                throw new Error(`Failed to fetch room type analysis. Cancellation Policy: ${cancellationPolicyResponse.status}`);
            }

            if (!payStayResponse.ok) {
                const payStayError = await payStayResponse.text();
                console.error('Error details:', {
                    payStay: payStayError
                });
                throw new Error(`Failed to fetch room type analysis. Pay Stay: ${payStayResponse.status}`);
            }
            if (!roomCapacityResponse.ok) {
                const roomCapacityResponseError = await roomCapacityResponse.text();
                console.error('Error details:', {
                    roomCapacity: roomCapacityResponseError
                });
                throw new Error(`Failed to fetch room type analysis. Capacity: ${roomCapacityResponse.status}`);
            }
            if (!mealTypeResponse.ok) {
                const mealTypeResponseError = await mealTypeResponse.text();
                console.error('Error details:', {
                    mealType: mealTypeResponseError
                });
                throw new Error(`Failed to fetch room type analysis. MealType: ${mealTypeResponse.status}`);
            }


            const minNightStayData = await minNightStayResponse.json();
            const cancellationPolicyData = await cancellationPolicyResponse.json();
            const payStayData = await payStayResponse.json();
            const roomCapacityData = await roomCapacityResponse.json();
            const mealTypeData = await mealTypeResponse.json();

            console.log("minNightStayData",minNightStayData);
            console.log("cancellationPolicyData",cancellationPolicyData);
            console.log("payStayData",payStayData);
            console.log("roomCapacityData",roomCapacityData)
            console.log("mealTypeData",mealTypeData)


            // Update state with min night stay
            setRoomTypeAnalysis(prev => ({
                ...prev,
                minNightStay: minNightStayData,
                cancellationPolicyData: cancellationPolicyData,
                payStayData: payStayData,
                mealTypeData: mealTypeData,
                roomCapacityData: roomCapacityData
            }));
            // Initialize editableMinNightStay when data is fetched
            if (minNightStayData && minNightStayData.min_night_stay !== undefined) {
                setEditableMinNightStay(minNightStayData.min_night_stay);
            } else {
                setEditableMinNightStay(''); // Default to empty if not available
            }

            // Initialize editableCancellationPolicies
            if (cancellationPolicyData && cancellationPolicyData.cancellation_policy && cancellationPolicyData.cancellation_policy.POLICIES && cancellationPolicyData.cancellation_policy.POLICIES.length > 0) {
                setEditableCancellationPolicies(cancellationPolicyData.cancellation_policy.POLICIES);
            } else {
                setEditableCancellationPolicies([{ CANCELLATION_FEE: '', START_DAY: '' }]); // Default for empty inputs
            }

            // Initialize editablePayStayOffers
            if (payStayData && payStayData.pay_stays && payStayData.pay_stays.OFFERS && payStayData.pay_stays.OFFERS.length > 0) {
                setEditablePayStayOffers(payStayData.pay_stays.OFFERS);
            } else {
                setEditablePayStayOffers([{ PAY_STAY_DAYS: '', PAY_STAY_FREE_NIGHTS: '' }]); // Default for empty inputs
            }

            // Initialize editableMealType
            if (mealTypeData && mealTypeData.meal_type !== undefined) {
                setEditableMealType(mealTypeData.meal_type);
            } else {
                setEditableMealType(''); // Default to empty string
            }

            // Initialize editableRoomCapacity
            if (roomCapacityData && roomCapacityData.capacity) {
                setEditableRoomCapacity({
                    BASE_CAPACITY: roomCapacityData.capacity.BASE_CAPACITY !== undefined ? roomCapacityData.capacity.BASE_CAPACITY : '',
                    TOTAL_CAPACITY: roomCapacityData.capacity.TOTAL_CAPACITY !== undefined ? roomCapacityData.capacity.TOTAL_CAPACITY : '',
                    MAX_ADULTS: roomCapacityData.capacity.MAX_ADULTS !== undefined ? roomCapacityData.capacity.MAX_ADULTS : '',
                    MAX_CHILDREN: roomCapacityData.capacity.MAX_CHILDREN !== undefined ? roomCapacityData.capacity.MAX_CHILDREN : ''
                });
            } else {
                setEditableRoomCapacity({ BASE_CAPACITY: '', TOTAL_CAPACITY: '', MAX_ADULTS: '', MAX_CHILDREN: '' }); // Default for empty inputs
            }

            const totalCapacity = roomCapacityData && roomCapacityData.capacity ? roomCapacityData.capacity.TOTAL_CAPACITY : 0;

            // Update state with total capacity
            setRoomTypeAnalysis(prev => ({
                ...prev,
                totalCapacity
            }));

            // Initialize rates array with empty objects for each period
            console.log("groupedPeriodsData", groupedPeriodsData)
            const initialRates = groupedPeriodsData.map(period => ({
                period: period.name,
                dateRanges: period.dateRanges,
                rates: period.dateRanges.map(() => ({
                    single: null,
                    double: null,
                    triple: null,
                    quad: null,
                    child: null,
                    infant: null
                }))
            }));

            setRoomTypeAnalysis(prev => ({
                ...prev,
                rates: initialRates
            }));
            setEditableRoomRates(initialRates); // Initialize editableRoomRates

            // Fetch rates for each period and date range
            for (let i = 0; i < groupedPeriodsData.length; i++) {
                const period = groupedPeriodsData[i];
                console.log("Processing period:", period);
                for (let j = 0; j < period.dateRanges.length; j++) {
                    const dateRange = period.dateRanges[j];
                    const periodData = [period.name, dateRange.start, dateRange.end];
                    
                    try {
                        // Fetch single room rate
                        console.log('Fetching single room rate for period:', periodData);
                        const singleResponse = await fetch(`${baseUrl}/room-single-room-rate`, {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ 
                                filename: filename,
                                property_name: propertyName,
                                room_type: roomType,
                                period: periodData,
                                model: currentModel
                            }),
                        });
                        if (singleResponse.ok) {
                            const singleData = await singleResponse.json();
                            console.log("Single room rate data:", singleData);
                            
                            // Update the rates array with the new data
                            setRoomTypeAnalysis(prev => {
                                const newRates = [...prev.rates];
                                if (newRates[i] && newRates[i].rates[j]) {
                                    newRates[i].rates[j].single = singleData;
                                } else {
                                    console.warn(`Index out of bounds: i=${i}, j=${j}, newRates.length=${newRates.length}, newRates[i]=${newRates[i]}`);
                                }
                                return { ...prev, rates: newRates };
                            });
                            // Update editableRoomRates as well
                            setEditableRoomRates(prevEditableRates => {
                                const newEditableRates = JSON.parse(JSON.stringify(prevEditableRates)); // Deep copy
                                if (newEditableRates[i] && newEditableRates[i].rates[j]) {
                                    newEditableRates[i].rates[j].single = singleData;
                                }
                                return newEditableRates;
                            });
                        } else {
                            console.error("Error getting single Room Rate:", await singleResponse.text());
                        }

                        // Fetch double room rate if capacity >= 2
                        if (totalCapacity >= 2) {
                            const doubleResponse = await fetch(`${baseUrl}/room-double-room-rate`, {
                                method: 'POST',
                                headers: { 'Content-Type': 'application/json' },
                                body: JSON.stringify({ 
                                    filename: filename,
                                    property_name: propertyName,
                                    room_type: roomType,
                                    period: periodData,
                                    model: currentModel
                                }),
                            });
                            if (doubleResponse.ok) {
                                const doubleData = await doubleResponse.json();
                                setRoomTypeAnalysis(prev => {
                                    const newRates = [...prev.rates];
                                    if (newRates[i] && newRates[i].rates[j]) {
                                        newRates[i].rates[j].double = doubleData; 
                                    } else {
                                        console.warn(`Index out of bounds: i=${i}, j=${j}, newRates.length=${newRates.length}, newRates[i]=${newRates[i]}`);
                                    }
                                    return { ...prev, rates: newRates };
                                });
                                // Update editableRoomRates as well
                                setEditableRoomRates(prevEditableRates => {
                                    const newEditableRates = JSON.parse(JSON.stringify(prevEditableRates)); // Deep copy
                                    if (newEditableRates[i] && newEditableRates[i].rates[j]) {
                                        newEditableRates[i].rates[j].double = doubleData;
                                    }
                                    return newEditableRates;
                                });
                            } else {
                                console.error("Error getting double Room Rate:", await doubleResponse.text());
                            }
                        }
                        


                        // Continue with triple, quad, child, and infant rates...
                        // (Similar pattern for other rate types)
                        
                    } catch (err) {
                        console.error(`Error fetching rates for period ${period.name} date range ${j}:`, err);
                        setRoomTypeAnalysis(prev => ({
                            ...prev,
                            rates: prev.rates.map((r, periodIndex) => 
                                periodIndex === i ? {
                                    ...r,
                                    rates: r.rates.map((rate, rangeIndex) => 
                                        rangeIndex === j ? { ...rate, error: err.message } : rate
                                    )
                                } : r
                            )
                        }));
                    }
                }
            }
        } catch (err) {
            console.error('Full error:', err);
            setError(err.message);
        } finally {
            setLoading(false);
        }
    };

    const handleProceed = async () => {
        if (!workflowState || !roomTypeList) {
             setError("Cannot proceed, workflow state or room list missing.");
             return;
        }

        const nextRoomTypeIndex = currentRoomTypeIndex + 1;

        //create a dictionary to represent the row in the csv: 
        // "Property", "Room type", "Meal basis", "Includes", "Excludes", "Child 1 From age", "Child 1 To age", "Max adults", "Max Children", "Max A+C", "Cancellation Policy from days 1", "Cancellation fee % 1", "Pay stay days", "Pay stay free nights"
        const csvRows = generateRows();
        
        if (!csvFileID) {
            console.error("File ID not found in localStorage. Cannot append rows.");
            setError("File ID not found. Please ensure the file is initialized.");
        } else {
            try {
                // Append each row sequentially
                for (const csvRow of csvRows) {
                    const response = await fetch(`${baseUrl}/append-row`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            file_id: csvFileID,
                            data: csvRow
                        }),
                    });

                    if (!response.ok) {
                        const errorText = await response.text();
                        throw new Error(`Failed to append row: ${errorText}`);
                    }
                }

                console.log("All rows successfully appended to the file.");
            } catch (err) {
                console.error("Error appending rows:", err);
                setError(`Failed to append rows: ${err.message}`);
            }
        }

        // Check if there are more room types for the CURRENT property
        if (nextRoomTypeIndex < roomTypeList.length) {
            // --- Move to the next room type ---
            console.log(`Workflow: Moving to next room type index: ${nextRoomTypeIndex}`);
            setCurrentRoomTypeIndex(nextRoomTypeIndex); // Update index state
            const nextRoomType = roomTypeList[nextRoomTypeIndex];
            setSelectedRoomType(nextRoomType); // Update selected room state
            setRoomTypeAnalysis(null); // Clear previous analysis
            setError(null); // Clear previous errors

            // Fetch analysis for the new room type
            fetchRoomTypeAnalysis(
                nextRoomType,
                selectedProperty, // Already in state
                workflowState.documentFilename, // From workflow state
                periods, // Already in state
                groupedPeriods // Already in state
            );

        } else {
            // --- Last room type for this property is done, move to next property or finish ---
            console.log(`Workflow: Finished all room types for property: ${selectedProperty}`);
            const nextPropertyIndex = workflowState.currentPropertyIndex + 1;

            // Create a mutable copy to update
            let updatedWorkflowState = { ...workflowState };
            updatedWorkflowState.currentPropertyIndex = nextPropertyIndex;

            // Check if there are more properties in the overall workflow
            if (nextPropertyIndex < workflowState.totalProperties) {
                // --- Go to the next property ---
                console.log(`Workflow: Proceeding to next property index: ${nextPropertyIndex}`);
                 try {
                    // Clear room-specific details before saving for next property page
                    delete updatedWorkflowState.currentPropertyRoomTypes;
                    localStorage.setItem('cameFromRoomTypeAnalysisLastStep', 'true'); // Added flag
                    localStorage.setItem('workflowState', JSON.stringify(updatedWorkflowState));
                    // Navigate back to property analysis page
                    navigate('/property-analysis', { state: { file: pdfFile } });
                 } catch (error) {
                     setError("Failed to save workflow state before navigating to next property.");
                     console.error("localStorage error:", error);
                 }
            } else {
                // --- All properties and room types are done ---
                console.log("Workflow: All properties processed. Checking for next section.");
                localStorage.setItem('cameFromRoomTypeAnalysisLastStep', 'true'); // Also set here

                // Check if there are more sections to process
                const nextSectionIndex = workflowState.currentSectionIndex + 1;
                if (nextSectionIndex < workflowState.sectionNames.length) {
                    // Update workflow state for next section
                     const updatedWorkflowStateForNextSection = { // Renamed variable to avoid conflict
                        ...workflowState,
                        currentSectionIndex: nextSectionIndex,
                        currentPropertyIndex: 0, // Reset property index for new section
                       documentFilename: workflowState.sectionNames[nextSectionIndex].name, // Update filename for new section
                        // Clear property-specific data for the new section
                        currentPropertyRoomTypes: undefined,
                        currentPropertyPeriods: undefined,
                        currentPropertyGroupedPeriods: undefined,
                    };
                    delete updatedWorkflowStateForNextSection.currentPropertyRoomTypes;
                    delete updatedWorkflowStateForNextSection.currentPropertyPeriods;
                    delete updatedWorkflowStateForNextSection.currentPropertyGroupedPeriods;

                    // Store updated workflow state
                    localStorage.setItem('workflowState', JSON.stringify(updatedWorkflowStateForNextSection));

                    // Navigate back to the next section
                    navigate('/', { state: { file: pdfFile } });
                } else {
                    // No more sections, proceed with CSV download and cleanup
                    // The cameFromRoomTypeAnalysisLastStep flag is already set above
                    // App.jsx will handle clearing it upon loading.
                    fetch(`${baseUrl}/download-csv/${csvFileID}`, {
                        method: 'GET',
                    })
                    .then(response => {
                        if (!response.ok) {
                            return response.text().then(text => {
                                throw new Error(`Failed to download CSV (HTTP ${response.status}): ${text}`);
                            });
                        }
                        
                        let filename = workflowState.documentFilename || `extracted_data_${csvFileID}.csv`;
                        if (filename.toLowerCase().endsWith('.txt')){
                            filename = filename.slice(0, -4); // Remove .txt extension if present
                        }
                        if (!filename.toLowerCase().endsWith('.csv')) {
                            filename += '.csv';
                        }
                        return response.blob().then(blob => ({ blob, filename }));
                    })
                    .then(({ blob, filename }) => {
                        const url = window.URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.style.display = 'none';
                        a.href = url;
                        a.download = filename;
                        document.body.appendChild(a);
                        a.click();
                        
                        window.URL.revokeObjectURL(url);
                        document.body.removeChild(a);
                        
                        console.log("CSV download initiated successfully.");
                        
                        try {
                            localStorage.removeItem('workflowState');
                            // localStorage.removeItem('pdfData'); // No longer used
                            localStorage.removeItem('csvFileID');
                            navigate('/');
                        } catch (error) {
                            setError("Failed to clear workflow state after completion.");
                            console.error("localStorage error:", error);
                        }
                    })
                    .catch(err => {
                        console.error("Error during CSV download process:", err);
                        setError(`Failed to download CSV. ${err.message}. Please check the console for details.`);
                    });
                }
            }
      
        }
    };

    const handleCancellationPolicyChange = (index, field, value) => {
        const updatedPolicies = [...editableCancellationPolicies];
        updatedPolicies[index] = { ...updatedPolicies[index], [field]: value };
        setEditableCancellationPolicies(updatedPolicies);
    };

    const handlePayStayChange = (index, field, value) => {
        const updatedOffers = [...editablePayStayOffers];
        updatedOffers[index] = { ...updatedOffers[index], [field]: value };
        setEditablePayStayOffers(updatedOffers);
    };

    const handleRoomCapacityChange = (field, value) => {
        setEditableRoomCapacity(prev => ({ ...prev, [field]: value }));
    };

    const handleRateChange = (periodIndex, rangeIndex, rateType, field, value) => {
        setEditableRoomRates(prevRates => {
            const newRates = JSON.parse(JSON.stringify(prevRates)); // Deep copy
            if (newRates[periodIndex] && newRates[periodIndex].rates[rangeIndex] && newRates[periodIndex].rates[rangeIndex][rateType] && newRates[periodIndex].rates[rangeIndex][rateType][`${rateType}_room_rate`]) {
                newRates[periodIndex].rates[rangeIndex][rateType][`${rateType}_room_rate`][field] = parseFloat(value) || 0;
            } else if (newRates[periodIndex] && newRates[periodIndex].rates[rangeIndex] && newRates[periodIndex].rates[rangeIndex][rateType] && newRates[periodIndex].rates[rangeIndex][rateType][`${rateType}_rate`]) { // For child/infant rates (ensure field is RATE)
                 newRates[periodIndex].rates[rangeIndex][rateType][`${rateType}_rate`][field] = parseFloat(value) || 0;
            }
            return newRates;
        });
    };

    const handleCancelEdit = () => {
        if (roomTypeAnalysis) {
            setEditableMinNightStay(roomTypeAnalysis.minNightStay?.min_night_stay ?? '');
            
            setEditableCancellationPolicies(
                roomTypeAnalysis.cancellationPolicyData?.cancellation_policy?.POLICIES?.map(p => ({...p})) || [{ CANCELLATION_FEE: '', START_DAY: '' }]
            );
            
            setEditablePayStayOffers(
                roomTypeAnalysis.payStayData?.pay_stays?.OFFERS?.map(o => ({...o})) || [{ PAY_STAY_DAYS: '', PAY_STAY_FREE_NIGHTS: '' }]
            );

            setEditableMealType(roomTypeAnalysis.mealTypeData?.meal_type ?? '');

            setEditableRoomCapacity({
                BASE_CAPACITY: roomTypeAnalysis.roomCapacityData?.capacity?.BASE_CAPACITY ?? '',
                TOTAL_CAPACITY: roomTypeAnalysis.roomCapacityData?.capacity?.TOTAL_CAPACITY ?? '',
                MAX_ADULTS: roomTypeAnalysis.roomCapacityData?.capacity?.MAX_ADULTS ?? '',
                MAX_CHILDREN: roomTypeAnalysis.roomCapacityData?.capacity?.MAX_CHILDREN ?? '',
            });

            // For editableRoomRates, reset based on roomTypeAnalysis.rates if available, 
            // otherwise reconstruct from initialGroupedPeriods to ensure UI structure
            if (roomTypeAnalysis.rates && roomTypeAnalysis.rates.length > 0) {
                 // Deep copy from roomTypeAnalysis.rates
                setEditableRoomRates(JSON.parse(JSON.stringify(roomTypeAnalysis.rates)));
            } else if (initialGroupedPeriods.length > 0) {
                const reconstructedInitialRates = initialGroupedPeriods.map(period => ({
                    period: period.name,
                    dateRanges: period.dateRanges,
                    rates: period.dateRanges.map(() => ({
                        single: null, double: null, triple: null, quad: null, child: null, infant: null
                    }))
                }));
                setEditableRoomRates(reconstructedInitialRates);
            } else {
                setEditableRoomRates([]); // Fallback to empty if no data at all
            }

        } else {
            // If roomTypeAnalysis itself is null, reset to initial default states
            setEditableMinNightStay('');
            setEditableCancellationPolicies([{ CANCELLATION_FEE: '', START_DAY: '' }]);
            setEditablePayStayOffers([{ PAY_STAY_DAYS: '', PAY_STAY_FREE_NIGHTS: '' }]);
            setEditableMealType('');
            setEditableRoomCapacity({ BASE_CAPACITY: '', TOTAL_CAPACITY: '', MAX_ADULTS: '', MAX_CHILDREN: '' });
            setEditableRoomRates([]);
        }
    };
    // Function to map complex search terms to more effective search terms
    const mapSearchTerm = (itemText) => {
        if (!itemText || typeof itemText !== 'string') {
            return itemText;
        }

        const text = itemText.trim();

        // Handle section headers
        if (text === 'Meal Type') return 'meal';
        if (text === 'Minimum Night Stay') return 'minimum night';
        if (text === 'Room Rates') return 'rate';
        if (text === 'Single Room Rate') return 'single';
        if (text === 'Double Room Rate') return 'double';
        if (text === 'Base Capacity') return 'capacity';
        if (text === 'Total Capacity') return 'capacity';
        if (text === 'Max Adults') return 'adult';
        if (text === 'Max Children') return 'child';

        // Handle meal type mappings - extract key terms
        if (text.includes('B&B') || text.includes('Bed & Breakfast')) return 'breakfast';
        if (text.includes('HB') || text.includes('Half Board')) return 'half board';
        if (text.includes('FB') || text.includes('Full Board')) return 'full board';
        if (text.includes('AI') || text.includes('All-Inclusive') || text.includes('All Inclusive')) return 'all inclusive';
        if (text.includes('RO') || text.includes('Room Only')) return 'room only';
        if (text.includes('SC') || text.includes('Self Catering')) return 'self catering';

        // Handle rate-related terms
        if (text === 'Base Rate') return 'rate';
        if (text === 'Additional People Cost') return 'additional';
        if (text === 'Additional People') return 'additional';

        // Handle stay-related terms
        if (text === 'Minimum Nights') return 'minimum night';
        if (text === 'Pay Stay Days') return 'pay stay';
        if (text === 'Pay Stay Free Nights') return 'free night';

        // Handle cancellation terms
        if (text.includes('Cancellation')) return 'cancellation';
        if (text.includes('Fee')) return 'fee';

        // For numeric values that might be too specific, keep them as-is
        // but consider the context
        if (text.match(/^\d+$/) && parseInt(text) < 100) {
            // Small numbers could be nights, capacity, etc. - keep as-is for now
            return text;
        }

        // For currency amounts, keep as-is
        if (text.match(/^R?\d+(\.\d{2})?$/) || text.includes('R')) {
            return text;
        }

        // Default: return the original text
        return text;
    };

    const handleItemClick = (itemText) => {
        const mappedSearchTerm = mapSearchTerm(itemText);
        setSearchText(mappedSearchTerm);
        setSearchStatus('searching'); // Indicate that viewer should start searching
    };

    const handleSearchResult = (status) => {
        setSearchStatus(status); // Update status based on search result from viewer (e.g., 'found', 'not_found')
    };

    const handleMinNightStayEdit = () => setEditingMinNightStay(true);
    const handleMinNightStaySave = () => { setEditingMinNightStay(false); /* Future: Potentially update roomTypeAnalysis if it were structured to hold all editable fields directly */ };
    const handleMinNightStayCancel = () => { setEditingMinNightStay(false); if (roomTypeAnalysis?.minNightStay) setEditableMinNightStay(roomTypeAnalysis.minNightStay.min_night_stay ?? ''); else setEditableMinNightStay(''); };

    const handleCancellationPolicyEdit = () => setEditingCancellationPolicy(true);
    const handleCancellationPolicySave = () => { setEditingCancellationPolicy(false); /* Update logic might be needed if roomTypeAnalysis is the source of truth */ };
    const handleCancellationPolicyCancel = () => { setEditingCancellationPolicy(false); if (roomTypeAnalysis?.cancellationPolicyData?.cancellation_policy?.POLICIES) setEditableCancellationPolicies(JSON.parse(JSON.stringify(roomTypeAnalysis.cancellationPolicyData.cancellation_policy.POLICIES))); else setEditableCancellationPolicies([{ CANCELLATION_FEE: '', START_DAY: '' }]); };
    const handleCancellationPolicyAdd = () => { setEditableCancellationPolicies([...editableCancellationPolicies, { CANCELLATION_FEE: '', START_DAY: '' }]); };
    const handleCancellationPolicyRemove = (index) => { setEditableCancellationPolicies(editableCancellationPolicies.filter((_, i) => i !== index)); };

    const handlePayStayEdit = () => setEditingPayStay(true);
    const handlePayStaySave = () => { setEditingPayStay(false); };
    const handlePayStayCancel = () => { setEditingPayStay(false); if (roomTypeAnalysis?.payStayData?.pay_stays?.OFFERS) setEditablePayStayOffers(JSON.parse(JSON.stringify(roomTypeAnalysis.payStayData.pay_stays.OFFERS))); else setEditablePayStayOffers([{ PAY_STAY_DAYS: '', PAY_STAY_FREE_NIGHTS: '' }]); };
    const handlePayStayAdd = () => { setEditablePayStayOffers([...editablePayStayOffers, { PAY_STAY_DAYS: '', PAY_STAY_FREE_NIGHTS: '' }]); };
    const handlePayStayRemove = (index) => { setEditablePayStayOffers(editablePayStayOffers.filter((_, i) => i !== index)); };

    const handleMealTypeEdit = () => setEditingMealType(true);
    const handleMealTypeSave = () => { setEditingMealType(false); };
    const handleMealTypeCancel = () => { setEditingMealType(false); if (roomTypeAnalysis?.mealTypeData) setEditableMealType(roomTypeAnalysis.mealTypeData.meal_type ?? ''); else setEditableMealType(''); };

    const handleCapacityEdit = () => setEditingCapacity(true);
    const handleCapacitySave = () => { setEditingCapacity(false); };
    const handleCapacityCancel = () => { setEditingCapacity(false); if (roomTypeAnalysis?.roomCapacityData?.capacity) setEditableRoomCapacity(JSON.parse(JSON.stringify(roomTypeAnalysis.roomCapacityData.capacity))); else setEditableRoomCapacity({ BASE_CAPACITY: '', TOTAL_CAPACITY: '', MAX_ADULTS: '', MAX_CHILDREN: '' }); };

    const handleRatesEdit = () => setEditingRates(true);
    const handleRatesSave = () => { setEditingRates(false); };
    const handleRatesCancel = () => { setEditingRates(false); if (roomTypeAnalysis?.rates && roomTypeAnalysis.rates.length > 0) { setEditableRoomRates(JSON.parse(JSON.stringify(roomTypeAnalysis.rates))); } else if (initialGroupedPeriods.length > 0) { const reconstructedInitialRates = initialGroupedPeriods.map(period => ({ period: period.name, dateRanges: period.dateRanges, rates: period.dateRanges.map(() => ({ single: null, double: null, triple: null, quad: null, child: null, infant: null })) })); setEditableRoomRates(reconstructedInitialRates); } else { setEditableRoomRates([]); } };

    const renderSearchStatusIcon = (itemText) => {
        if (searchText === itemText) {
            if (searchStatus === 'searching') {
                return <span className="ml-2 animate-spin">⏳</span>; // Spinner
            } else if (searchStatus === 'found') {
                return <span className="ml-2 text-green-500">✔️</span>; // Checkmark
            } else if (searchStatus === 'not_found') {
                return <span className="ml-2 text-red-500">❌</span>; // Cross
            }
        }
        return null;
    };
    

    return (
        <div className="min-h-screen bg-gray-100 py-6">
            <div className="container mx-auto px-4">
                <div className="flex justify-between items-center mb-8">
                    <h1 className="text-3xl font-bold">Room Type Analysis</h1>
                    <button
                        onClick={handleBackNavigation}
                        className="px-4 py-2 text-blue-500 hover:text-blue-700"
                    >
                        ← Back
                    </button>
                </div>
                
                {error && (
                    <div className="mb-8 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
                        {error}
                    </div>
                )}
                
                {/* Add Model Selector */}
                <div className="mb-3">
                    <label htmlFor="modelSelect" className="form-label">Select Model:</label>
                    <select 
                        id="modelSelect" 
                        className="form-select" 
                        value={selectedModel} 
                        onChange={(e) => {
                            setSelectedModel(e.target.value);
                            localStorage.setItem('selectedModel', e.target.value);
                        }}
                    >
                        {modelOptions.map(option => (
                            <option key={option.value} value={option.value}>
                                {option.label}
                            </option>
                        ))}
                    </select>
                </div>
                
                {/* Main Content Area - Two Column Layout */}
                <div className="grid grid-cols-1 lg:grid-cols-5 gap-8 items-start">
                    {/* Left Column - Room Type Selection and Analysis */}
                    <div className="lg:col-span-2 flex flex-col gap-6">               
                        {/* Progress Display */}
                        {workflowState && selectedProperty && roomTypeList.length > 0 && (
                            <div className="bg-white p-4 rounded-lg shadow-md flex-grow">
                                <h2 className="text-xl font-semibold mb-2 text-gray-800">
                                    Property: <span className="font-bold">{selectedProperty}</span>
                                </h2>
                                <p className="text-md font-medium text-blue-700">
                                    Analyzing Room Type {currentRoomTypeIndex + 1} / {roomTypeList.length}:
                                    <span onClick={() => handleItemClick(selectedRoomType)} className="ml-2 font-bold cursor-pointer hover:underline">{selectedRoomType}</span>
                                    {renderSearchStatusIcon(selectedRoomType)}
                                </p>
                                {workflowState && typeof workflowState.currentPropertyIndex === 'number' && typeof workflowState.totalProperties === 'number' && // Ensure properties exist and are numbers
                                    <p className="text-sm text-gray-600">
                                        (Property {workflowState.currentPropertyIndex + 1} / {workflowState.totalProperties} in workflow)
                                    </p>
                                }
                            </div>
                        )}
                        {/* Room Type Analysis Results */}
                        <div className="bg-white p-6 rounded-lg shadow-md flex-grow">
                            <div className="flex justify-between items-center mb-4">
                                <h2 className="text-xl font-semibold">Analysis Results for {selectedRoomType}</h2>
                                {/* Placeholder for future individual edit buttons if needed at top level */}
                            </div>
                            {/* ... Loading indicator ... */}
                            
                            {loading && (
                                <div className="flex justify-center items-center h-40">
                                    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
                                </div>
                            )}
                            
                            {roomTypeAnalysis && (
                                <div className="space-y-6">
                                    {/* Minimum Night Stay Section */}
                                    <div>
                                        <div className="flex justify-between items-center mb-1">
                                            <h3 onClick={() => handleItemClick('Minimum Night Stay')} className="text-lg font-medium text-blue-600 cursor-pointer hover:underline">Minimum Night Stay</h3>
                                            {!editingMinNightStay && (
                                                <button
                                                    onClick={handleMinNightStayEdit}
                                                    className="px-4 py-2 text-blue-500 hover:text-blue-700"
                                                >
                                                    Edit Min Night Stay
                                                </button>
                                            )}
                                        </div>
                                        <div className="bg-blue-50 p-4 rounded-lg">
                                            {editingMinNightStay ? (
                                                <React.Fragment>
                                                    <label htmlFor="minNightStayInput" className="block text-sm font-medium text-gray-700">
                                                        Minimum Nights:
                                                    </label>
                                                    <input
                                                        type="number"
                                                        id="minNightStayInput"
                                                        value={editableMinNightStay}
                                                        onChange={(e) => setEditableMinNightStay(e.target.value)}
                                                        className="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                                        placeholder="Enter minimum nights"
                                                    />
                                                    <div className="flex justify-end gap-2 mt-3">
                                                        <button onClick={handleMinNightStayCancel} className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-100">Cancel</button>
                                                        <button onClick={handleMinNightStaySave} className="px-4 py-2 text-sm bg-green-500 text-white rounded-lg hover:bg-green-600">Save</button>
                                                    </div>
                                                </React.Fragment>
                                            ) : (
                                                <React.Fragment>
                                                    <p><strong onClick={() => handleItemClick('Minimum Nights')} className="cursor-pointer hover:underline">Minimum Nights:</strong> <span onClick={() => handleItemClick(String(editableMinNightStay || ''))} className="cursor-pointer hover:underline">{editableMinNightStay !== null && editableMinNightStay !== '' ? editableMinNightStay : 'N/A'}</span>{renderSearchStatusIcon(String(editableMinNightStay || ''))}</p>
                                                    {roomTypeAnalysis.minNightStay && roomTypeAnalysis.minNightStay.error && !editingMinNightStay && (
                                                        <p className="text-red-500 text-xs italic"><strong>Error:</strong> {roomTypeAnalysis.minNightStay.error}</p>
                                                    )}
                                                    {/* Edit button moved to header, if needed here, it would be:
                                                    <div className="flex justify-end mt-4">
                                                        <button onClick={handleMinNightStayEdit} className="px-4 py-2 text-blue-500 hover:text-blue-700">Edit Min Night Stay</button>
                                                    </div>
                                                    */}
                                                </React.Fragment>
                                            )}
                                        </div>
                                    </div>

                                    {/* Cancellation Policy Section */}
                                    <div>
                                        <div className="flex justify-between items-center mb-1">
                                            <h3 onClick={() => handleItemClick('Cancellation Policy')} className="text-lg font-medium text-red-600 cursor-pointer hover:underline">Cancellation Policy</h3>
                                            {!editingCancellationPolicy && (
                                                <button
                                                    onClick={handleCancellationPolicyEdit}
                                                    className="px-4 py-2 text-blue-500 hover:text-blue-700"
                                                >
                                                    Edit Cancellation Policy
                                                </button>
                                            )}
                                        </div>
                                        <div className="bg-red-50 p-4 rounded-lg">
                                            {editingCancellationPolicy ? (
                                                <React.Fragment>
                                                    {editableCancellationPolicies.map((policy, index) => (
                                                        <div key={index} className="bg-white p-3 rounded shadow-sm space-y-2 mb-3">
                                                                <div>
                                                                    <label htmlFor={`cancelFee-${index}`} className="block text-sm font-medium text-gray-700">
                                                                        Cancellation Fee (% or Amount):
                                                                    </label>
                                                                    <input
                                                                        type="text"
                                                                        id={`cancelFee-${index}`}
                                                                        value={policy.CANCELLATION_FEE}
                                                                        onChange={(e) => handleCancellationPolicyChange(index, 'CANCELLATION_FEE', e.target.value)}
                                                                        className="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                                                        placeholder="e.g., 50% or 100"
                                                                    />
                                                                </div>
                                                                <div>
                                                                    <label htmlFor={`startDay-${index}`} className="block text-sm font-medium text-gray-700">
                                                                        Start Day (days before check-in):
                                                                    </label>
                                                                    <input
                                                                        type="number"
                                                                        id={`startDay-${index}`}
                                                                        value={policy.START_DAY}
                                                                        onChange={(e) => handleCancellationPolicyChange(index, 'START_DAY', e.target.value)}
                                                                        className="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                                                        placeholder="e.g., 14"
                                                                    />
                                                                </div>
                                                            <div className="flex justify-end">
                                                                <button onClick={() => handleCancellationPolicyRemove(index)} className="px-3 py-1 text-xs text-red-500 hover:text-red-700 border border-red-300 rounded-md hover:bg-red-50">Remove</button>
                                                            </div>
                                                        </div>
                                                    ))}
                                                    <button onClick={handleCancellationPolicyAdd} className="mt-2 px-4 py-2 text-sm text-white bg-blue-500 hover:bg-blue-600 rounded-lg">Add Policy</button>
                                                    <div className="flex justify-end gap-2 mt-3">
                                                        <button onClick={handleCancellationPolicyCancel} className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-100">Cancel</button>
                                                        <button onClick={handleCancellationPolicySave} className="px-4 py-2 text-sm bg-green-500 text-white rounded-lg hover:bg-green-600">Save</button>
                                                    </div>
                                                </React.Fragment>
                                            ) : (
                                                <React.Fragment>
                                                    {editableCancellationPolicies.map((policy, index) => (
                                                        <div key={index} className="bg-white p-3 rounded shadow-sm space-y-2 mb-2">
                                                            <p><strong onClick={() => handleItemClick('Cancellation Fee')} className="cursor-pointer hover:underline">Cancellation Fee:</strong> <span onClick={() => handleItemClick(String(policy.CANCELLATION_FEE || ''))} className="cursor-pointer hover:underline">{policy.CANCELLATION_FEE !== null && policy.CANCELLATION_FEE !== '' ? policy.CANCELLATION_FEE : 'N/A'}</span>{renderSearchStatusIcon(String(policy.CANCELLATION_FEE || ''))}</p>
                                                            <p><strong onClick={() => handleItemClick('Start Day')} className="cursor-pointer hover:underline">Start Day:</strong> <span onClick={() => handleItemClick(String(policy.START_DAY || ''))} className="cursor-pointer hover:underline">{policy.START_DAY !== null && policy.START_DAY !== '' ? policy.START_DAY : 'N/A'}</span>{renderSearchStatusIcon(String(policy.START_DAY || ''))}</p>
                                                        </div>
                                                    ))}
                                                    {roomTypeAnalysis && roomTypeAnalysis.cancellationPolicyData && roomTypeAnalysis.cancellationPolicyData.error && !editingCancellationPolicy && (
                                                        <p className="text-red-500 text-xs italic"><strong>Error:</strong> {roomTypeAnalysis.cancellationPolicyData.error}</p>
                                                    )}
                                                </React.Fragment>
                                            )}
                                        </div>
                                    </div>

                                    {/* Pay Stay Section */}
                                    <div>
                                        <div className="flex justify-between items-center mb-1">
                                            <h3 onClick={() => handleItemClick('Pay Stay Offers')} className="text-lg font-medium text-green-600 cursor-pointer hover:underline">Pay Stay Offers</h3>
                                            {!editingPayStay && (
                                                <button
                                                    onClick={handlePayStayEdit}
                                                    className="px-4 py-2 text-blue-500 hover:text-blue-700"
                                                >
                                                    Edit Pay Stay Offers
                                                </button>
                                            )}
                                        </div>
                                        <div className="bg-green-50 p-4 rounded-lg">
                                            {editingPayStay ? (
                                                <React.Fragment>
                                                    {editablePayStayOffers.map((offer, index) => (
                                                        <div key={index} className="bg-white p-3 rounded shadow-sm space-y-2 mb-3">
                                                                <div>
                                                                    <label htmlFor={`payDays-${index}`} className="block text-sm font-medium text-gray-700">
                                                                        Pay Stay Days:
                                                                    </label>
                                                                    <input
                                                                        type="number"
                                                                        id={`payDays-${index}`}
                                                                        value={offer.PAY_STAY_DAYS}
                                                                        onChange={(e) => handlePayStayChange(index, 'PAY_STAY_DAYS', e.target.value)}
                                                                        className="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                                                        placeholder="e.g., 7"
                                                                    />
                                                                </div>
                                                                <div>
                                                                    <label htmlFor={`freeNights-${index}`} className="block text-sm font-medium text-gray-700">
                                                                        Pay Stay Free Nights:
                                                                    </label>
                                                                    <input
                                                                        type="number"
                                                                        id={`freeNights-${index}`}
                                                                        value={offer.PAY_STAY_FREE_NIGHTS}
                                                                        onChange={(e) => handlePayStayChange(index, 'PAY_STAY_FREE_NIGHTS', e.target.value)}
                                                                        className="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                                                        placeholder="e.g., 1"
                                                                    />
                                                                </div>
                                                            <div className="flex justify-end">
                                                                <button onClick={() => handlePayStayRemove(index)} className="px-3 py-1 text-xs text-red-500 hover:text-red-700 border border-red-300 rounded-md hover:bg-red-50">Remove</button>
                                                            </div>
                                                        </div>
                                                    ))}
                                                    <button onClick={handlePayStayAdd} className="mt-2 px-4 py-2 text-sm text-white bg-blue-500 hover:bg-blue-600 rounded-lg">Add Offer</button>
                                                    <div className="flex justify-end gap-2 mt-3">
                                                        <button onClick={handlePayStayCancel} className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-100">Cancel</button>
                                                        <button onClick={handlePayStaySave} className="px-4 py-2 text-sm bg-green-500 text-white rounded-lg hover:bg-green-600">Save</button>
                                                    </div>
                                                </React.Fragment>
                                            ) : (
                                                <React.Fragment>
                                                    {editablePayStayOffers.map((offer, index) => (
                                                        <div key={index} className="bg-white p-3 rounded shadow-sm space-y-2 mb-2">
                                                            <p><strong onClick={() => handleItemClick('Pay Stay Days')} className="cursor-pointer hover:underline">Pay Stay Days:</strong> <span onClick={() => handleItemClick(String(offer.PAY_STAY_DAYS || ''))} className="cursor-pointer hover:underline">{offer.PAY_STAY_DAYS !== null && offer.PAY_STAY_DAYS !== '' ? offer.PAY_STAY_DAYS : 'N/A'}</span>{renderSearchStatusIcon(String(offer.PAY_STAY_DAYS || ''))}</p>
                                                            <p><strong onClick={() => handleItemClick('Pay Stay Free Nights')} className="cursor-pointer hover:underline">Pay Stay Free Nights:</strong> <span onClick={() => handleItemClick(String(offer.PAY_STAY_FREE_NIGHTS || ''))} className="cursor-pointer hover:underline">{offer.PAY_STAY_FREE_NIGHTS !== null && offer.PAY_STAY_FREE_NIGHTS !== '' ? offer.PAY_STAY_FREE_NIGHTS : 'N/A'}</span>{renderSearchStatusIcon(String(offer.PAY_STAY_FREE_NIGHTS || ''))}</p>
                                                        </div>
                                                    ))}
                                                    {roomTypeAnalysis && roomTypeAnalysis.payStayData && roomTypeAnalysis.payStayData.error && !editingPayStay && (
                                                        <p className="text-red-500 text-xs italic"><strong>Error:</strong> {roomTypeAnalysis.payStayData.error}</p>
                                                    )}
                                                </React.Fragment>
                                            )}
                                        </div>
                                    </div>

                                    {/* Meal Type Section */}
                                    <div>
                                        <div className="flex justify-between items-center mb-1">
                                            <h3 onClick={() => handleItemClick('Meal Type')} className="text-lg font-medium text-orange-600 cursor-pointer hover:underline">Meal Type</h3>
                                            {!editingMealType && (
                                                <button
                                                    onClick={handleMealTypeEdit}
                                                    className="px-4 py-2 text-blue-500 hover:text-blue-700"
                                                >
                                                    Edit Meal Type
                                                </button>
                                            )}
                                        </div>
                                        <div className="bg-orange-50 p-4 rounded-lg">
                                            {editingMealType ? (
                                                <React.Fragment>
                                                    <label htmlFor="mealTypeInput" className="block text-sm font-medium text-gray-700">
                                                        Meal Type:
                                                    </label>
                                                    <input
                                                        type="text"
                                                        id="mealTypeInput"
                                                        value={editableMealType}
                                                        onChange={(e) => setEditableMealType(e.target.value)}
                                                        className="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                                        placeholder="e.g., Bed & Breakfast"
                                                    />
                                                    <div className="flex justify-end gap-2 mt-3">
                                                        <button onClick={handleMealTypeCancel} className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-100">Cancel</button>
                                                        <button onClick={handleMealTypeSave} className="px-4 py-2 text-sm bg-green-500 text-white rounded-lg hover:bg-green-600">Save</button>
                                                    </div>
                                                </React.Fragment>
                                            ) : (
                                                <React.Fragment>
                                                    <p><strong onClick={() => handleItemClick('Meal Type')} className="cursor-pointer hover:underline">Meal Type:</strong> <span onClick={() => handleItemClick(String(editableMealType || ''))} className="cursor-pointer hover:underline">{editableMealType !== null && editableMealType !== '' ? editableMealType : 'N/A'}</span>{renderSearchStatusIcon(String(editableMealType || ''))}</p>
                                                    {roomTypeAnalysis && roomTypeAnalysis.mealTypeData && roomTypeAnalysis.mealTypeData.error && !editingMealType && (
                                                        <p className="text-red-500 text-xs italic"><strong>Error:</strong> {roomTypeAnalysis.mealTypeData.error}</p>
                                                    )}
                                                </React.Fragment>
                                            )}
                                        </div>
                                    </div>

                

                                    {/* Room Rates Section */}
                                    <div>
                                        <div className="flex justify-between items-center mb-1">
                                            <h3 onClick={() => handleItemClick('Room Rates')} className="text-lg font-medium text-purple-600 cursor-pointer hover:underline">Room Rates</h3>
                                            {!editingRates && !editingCapacity && ( // Hide if capacity is being edited too
                                                <button
                                                    onClick={handleRatesEdit}
                                                    className="px-4 py-2 text-blue-500 hover:text-blue-700"
                                                >
                                                    Edit Rates
                                                </button>
                                            )}
                                        </div>
                                        <div className="bg-purple-50 p-4 rounded-lg">
                                            {/* Editable Room Capacity Section */}
                                            <div className="bg-white p-3 rounded mb-4 shadow-sm">
                                                <div className="flex justify-between items-center mb-1">
                                                    <h5 onClick={() => handleItemClick('Room Capacity')} className="text-md font-medium text-gray-800 cursor-pointer hover:underline">Room Capacity</h5>
                                                    {!editingCapacity && !editingRates && ( // Hide if rates are being edited
                                                        <button
                                                            onClick={handleCapacityEdit}
                                                            className="px-4 py-2 text-blue-500 hover:text-blue-700"
                                                        >
                                                            Edit Capacity
                                                        </button>
                                                    )}
                                                </div>
                                                {editingCapacity && !editingRates ? (
                                                    <React.Fragment>
                                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                            <div>
                                                                <label htmlFor="baseCapacity" className="block text-sm font-medium text-gray-700">Base Capacity:</label>
                                                                <input type="number" id="baseCapacity" value={editableRoomCapacity.BASE_CAPACITY} onChange={(e) => handleRoomCapacityChange('BASE_CAPACITY', e.target.value)} className="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="e.g., 2"/>
                                                            </div>
                                                            <div>
                                                                <label htmlFor="totalCapacity" className="block text-sm font-medium text-gray-700">Total Capacity (Max A+C):</label>
                                                                <input type="number" id="totalCapacity" value={editableRoomCapacity.TOTAL_CAPACITY} onChange={(e) => handleRoomCapacityChange('TOTAL_CAPACITY', e.target.value)} className="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="e.g., 4"/>
                                                            </div>
                                                            <div>
                                                                <label htmlFor="maxAdults" className="block text-sm font-medium text-gray-700">Max Adults:</label>
                                                                <input type="number" id="maxAdults" value={editableRoomCapacity.MAX_ADULTS} onChange={(e) => handleRoomCapacityChange('MAX_ADULTS', e.target.value)} className="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="e.g., 2"/>
                                                            </div>
                                                            <div>
                                                                <label htmlFor="maxChildren" className="block text-sm font-medium text-gray-700">Max Children:</label>
                                                                <input type="number" id="maxChildren" value={editableRoomCapacity.MAX_CHILDREN} onChange={(e) => handleRoomCapacityChange('MAX_CHILDREN', e.target.value)} className="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="e.g., 2"/>
                                                            </div>
                                                        </div>
                                                        <div className="flex justify-end gap-2 mt-3">
                                                            <button onClick={handleCapacityCancel} className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-100">Cancel</button>
                                                            <button onClick={handleCapacitySave} className="px-4 py-2 text-sm bg-green-500 text-white rounded-lg hover:bg-green-600">Save</button>
                                                        </div>
                                                    </React.Fragment>
                                                ) : (
                                                    <React.Fragment>
                                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                            <p><strong onClick={() => handleItemClick('Base Capacity')} className="cursor-pointer hover:underline">Base Capacity:</strong> <span onClick={() => handleItemClick(String(editableRoomCapacity.BASE_CAPACITY || ''))} className="cursor-pointer hover:underline">{editableRoomCapacity.BASE_CAPACITY !== null && editableRoomCapacity.BASE_CAPACITY !== '' ? editableRoomCapacity.BASE_CAPACITY : 'N/A'}</span> people{renderSearchStatusIcon(String(editableRoomCapacity.BASE_CAPACITY || ''))}</p>
                                                            <p><strong onClick={() => handleItemClick('Total Capacity')} className="cursor-pointer hover:underline">Total Capacity (Max A+C):</strong> <span onClick={() => handleItemClick(String(editableRoomCapacity.TOTAL_CAPACITY || ''))} className="cursor-pointer hover:underline">{editableRoomCapacity.TOTAL_CAPACITY !== null && editableRoomCapacity.TOTAL_CAPACITY !== '' ? editableRoomCapacity.TOTAL_CAPACITY : 'N/A'}</span> people{renderSearchStatusIcon(String(editableRoomCapacity.TOTAL_CAPACITY || ''))}</p>
                                                            <p><strong onClick={() => handleItemClick('Max Adults')} className="cursor-pointer hover:underline">Max Adults:</strong> <span onClick={() => handleItemClick(String(editableRoomCapacity.MAX_ADULTS || ''))} className="cursor-pointer hover:underline">{editableRoomCapacity.MAX_ADULTS !== null && editableRoomCapacity.MAX_ADULTS !== '' ? editableRoomCapacity.MAX_ADULTS : 'N/A'}</span>{renderSearchStatusIcon(String(editableRoomCapacity.MAX_ADULTS || ''))}</p>
                                                            <p><strong onClick={() => handleItemClick('Max Children')} className="cursor-pointer hover:underline">Max Children:</strong> <span onClick={() => handleItemClick(String(editableRoomCapacity.MAX_CHILDREN || ''))} className="cursor-pointer hover:underline">{editableRoomCapacity.MAX_CHILDREN !== null && editableRoomCapacity.MAX_CHILDREN !== '' ? editableRoomCapacity.MAX_CHILDREN : 'N/A'}</span>{renderSearchStatusIcon(String(editableRoomCapacity.MAX_CHILDREN || ''))}</p>
                                                        </div>
                                                    </React.Fragment>
                                                )}
                                                {roomTypeAnalysis && roomTypeAnalysis.roomCapacityData && roomTypeAnalysis.roomCapacityData.error && !editingCapacity && !editingRates && (
                                                    <p className="text-red-500 text-xs italic mt-2"><strong>Error:</strong> {roomTypeAnalysis.roomCapacityData.error}</p>
                                                )}
                                            </div>

                                            {editingRates && (
                                                <div className="flex justify-end gap-2 mb-3 mt-4">  {/* Save/Cancel for Rates at top of rates list */}
                                                    <button onClick={handleRatesCancel} className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-100">Cancel Rates</button>
                                                    <button onClick={handleRatesSave} className="px-4 py-2 text-sm bg-green-500 text-white rounded-lg hover:bg-green-600">Save Rates</button>
                                                </div>
                                            )}
                                            {roomTypeAnalysis.rates && roomTypeAnalysis.rates.length > 0 ? (
                                                <div className="space-y-6">
                                                    {editableRoomRates.map((periodData, periodIndex) => (
                                                        <div key={periodIndex} className="border-b border-purple-200 pb-4 last:border-0">
                                                            <h4 className="text-lg font-semibold mb-3">{periodData.period}</h4>
                                                            
                                                            {periodData.dateRanges.map((dateRange, rangeIndex) => {
                                                                const currentRangeRates = periodData.rates[rangeIndex]; // This is from editableRoomRates
                                                                return (
                                                                    <div key={rangeIndex} className="mb-4 last:mb-0">
                                                                        <div className="text-sm text-gray-600 mb-2">
                                                                            {new Date(dateRange.start).toLocaleDateString()} - {new Date(dateRange.end).toLocaleDateString()}
                                                                        </div>
                                                                        
                                                                        {currentRangeRates.error ? (
                                                                            <p className="text-red-600 text-sm">{currentRangeRates.error}</p>
                                                                        ) : (
                                                                            <div className="space-y-4">
                                                                                {/* Single Room Rate */}
                                                                                {currentRangeRates.single && currentRangeRates.single.single_room_rate && (
                                                                                    <div className="bg-white p-3 rounded shadow-sm">
                                                                                        <h5 onClick={() => handleItemClick('Single Room Rate')} className="font-medium mb-2 cursor-pointer hover:underline">Single Room Rate</h5>
                                                                                        {editingRates ? (
                                                                                            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-2">
                                                                                                <div>
                                                                                                    <label htmlFor={`singleBaseRate-${periodIndex}-${rangeIndex}`} className="block text-xs font-medium text-gray-600">Base Rate (R):</label>
                                                                                                    <input type="number" id={`singleBaseRate-${periodIndex}-${rangeIndex}`} value={currentRangeRates.single.single_room_rate.BASE_RATE || ''} onChange={(e) => handleRateChange(periodIndex, rangeIndex, 'single', 'BASE_RATE', e.target.value)} className="mt-1 block w-full px-2 py-1 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="0"/>
                                                                                                </div>
                                                                                                <div>
                                                                                                    <label htmlFor={`singleAdditional-${periodIndex}-${rangeIndex}`} className="block text-xs font-medium text-gray-600">Additional People (R):</label>
                                                                                                    <input type="number" id={`singleAdditional-${periodIndex}-${rangeIndex}`} value={currentRangeRates.single.single_room_rate.ADDITIONAL_PEOPLE || ''} onChange={(e) => handleRateChange(periodIndex, rangeIndex, 'single', 'ADDITIONAL_PEOPLE', e.target.value)} className="mt-1 block w-full px-2 py-1 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="0"/>
                                                                                                </div>
                                                                                                <div className="md:col-span-2">
                                                                                                    <p className="font-semibold text-green-600 text-sm"><strong>Total Cost:</strong> R{(currentRangeRates.single.single_room_rate.BASE_RATE || 0) + (currentRangeRates.single.single_room_rate.ADDITIONAL_PEOPLE || 0)}</p>
                                                                                                </div>
                                                                                            </div>
                                                                                        ) : (
                                                                                            <div className="space-y-1">
                                                                                                <p><strong onClick={() => handleItemClick('Base Rate')} className="cursor-pointer hover:underline">Base Rate:</strong> R<span onClick={() => handleItemClick(String(currentRangeRates.single.single_room_rate.BASE_RATE || ''))} className="cursor-pointer hover:underline">{currentRangeRates.single.single_room_rate.BASE_RATE !== null && currentRangeRates.single.single_room_rate.BASE_RATE !== '' ? currentRangeRates.single.single_room_rate.BASE_RATE : 'N/A'}</span>{renderSearchStatusIcon(String(currentRangeRates.single.single_room_rate.BASE_RATE || ''))}</p>
                                                                                                <p><strong onClick={() => handleItemClick('Additional People Cost')} className="cursor-pointer hover:underline">Additional People Cost:</strong> R<span onClick={() => handleItemClick(String(currentRangeRates.single.single_room_rate.ADDITIONAL_PEOPLE || ''))} className="cursor-pointer hover:underline">{currentRangeRates.single.single_room_rate.ADDITIONAL_PEOPLE !== null && currentRangeRates.single.single_room_rate.ADDITIONAL_PEOPLE !== '' ? currentRangeRates.single.single_room_rate.ADDITIONAL_PEOPLE : 'N/A'}</span>{renderSearchStatusIcon(String(currentRangeRates.single.single_room_rate.ADDITIONAL_PEOPLE || ''))}</p>
                                                                                                <p className="font-semibold text-green-600"><strong onClick={() => handleItemClick('Total Cost')} className="cursor-pointer hover:underline">Total Cost:</strong> R<span onClick={() => handleItemClick(String((currentRangeRates.single.single_room_rate.BASE_RATE || 0) + (currentRangeRates.single.single_room_rate.ADDITIONAL_PEOPLE || 0)))} className="cursor-pointer hover:underline">{(currentRangeRates.single.single_room_rate.BASE_RATE || 0) + (currentRangeRates.single.single_room_rate.ADDITIONAL_PEOPLE || 0)}</span>{renderSearchStatusIcon(String((currentRangeRates.single.single_room_rate.BASE_RATE || 0) + (currentRangeRates.single.single_room_rate.ADDITIONAL_PEOPLE || 0)))}</p>
                                                                                            </div>
                                                                                        )}
                                                                                    </div>
                                                                                )}

                                                                                {/* Double Room Rate */}
                                                                                {currentRangeRates.double && currentRangeRates.double.double_room_rate && (
                                                                                    <div className="bg-white p-3 rounded shadow-sm">
                                                                                        <h5 onClick={() => handleItemClick('Double Room Rate')} className="font-medium mb-2 cursor-pointer hover:underline">Double Room Rate</h5>
                                                                                        {editingRates ? (
                                                                                            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-2">
                                                                                                <div>
                                                                                                    <label htmlFor={`doubleBaseRate-${periodIndex}-${rangeIndex}`} className="block text-xs font-medium text-gray-600">Base Rate (R):</label>
                                                                                                    <input type="number" id={`doubleBaseRate-${periodIndex}-${rangeIndex}`} value={currentRangeRates.double.double_room_rate.BASE_RATE || ''} onChange={(e) => handleRateChange(periodIndex, rangeIndex, 'double', 'BASE_RATE', e.target.value)} className="mt-1 block w-full px-2 py-1 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="0"/>
                                                                                                </div>
                                                                                                <div>
                                                                                                    <label htmlFor={`doubleAdditional-${periodIndex}-${rangeIndex}`} className="block text-xs font-medium text-gray-600">Additional People (R):</label>
                                                                                                    <input type="number" id={`doubleAdditional-${periodIndex}-${rangeIndex}`} value={currentRangeRates.double.double_room_rate.ADDITIONAL_PEOPLE || ''} onChange={(e) => handleRateChange(periodIndex, rangeIndex, 'double', 'ADDITIONAL_PEOPLE', e.target.value)} className="mt-1 block w-full px-2 py-1 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="0"/>
                                                                                                </div>
                                                                                                <div className="md:col-span-2">
                                                                                                    <p className="font-semibold text-green-600 text-sm"><strong>Total Cost:</strong> R{(currentRangeRates.double.double_room_rate.BASE_RATE || 0) + (currentRangeRates.double.double_room_rate.ADDITIONAL_PEOPLE || 0)}</p>
                                                                                                </div>
                                                                                            </div>
                                                                                        ) : (
                                                                                            <div className="space-y-1">
                                                                                                <p><strong onClick={() => handleItemClick('Base Rate')} className="cursor-pointer hover:underline">Base Rate:</strong> R<span onClick={() => handleItemClick(String(currentRangeRates.double.double_room_rate.BASE_RATE || ''))} className="cursor-pointer hover:underline">{currentRangeRates.double.double_room_rate.BASE_RATE !== null && currentRangeRates.double.double_room_rate.BASE_RATE !== '' ? currentRangeRates.double.double_room_rate.BASE_RATE : 'N/A'}</span>{renderSearchStatusIcon(String(currentRangeRates.double.double_room_rate.BASE_RATE || ''))}</p>
                                                                                                <p><strong onClick={() => handleItemClick('Additional People Cost')} className="cursor-pointer hover:underline">Additional People Cost:</strong> R<span onClick={() => handleItemClick(String(currentRangeRates.double.double_room_rate.ADDITIONAL_PEOPLE || ''))} className="cursor-pointer hover:underline">{currentRangeRates.double.double_room_rate.ADDITIONAL_PEOPLE !== null && currentRangeRates.double.double_room_rate.ADDITIONAL_PEOPLE !== '' ? currentRangeRates.double.double_room_rate.ADDITIONAL_PEOPLE : 'N/A'}</span>{renderSearchStatusIcon(String(currentRangeRates.double.double_room_rate.ADDITIONAL_PEOPLE || ''))}</p>
                                                                                                <p className="font-semibold text-green-600"><strong onClick={() => handleItemClick('Total Cost')} className="cursor-pointer hover:underline">Total Cost:</strong> R<span onClick={() => handleItemClick(String((currentRangeRates.double.double_room_rate.BASE_RATE || 0) + (currentRangeRates.double.double_room_rate.ADDITIONAL_PEOPLE || 0)))} className="cursor-pointer hover:underline">{(currentRangeRates.double.double_room_rate.BASE_RATE || 0) + (currentRangeRates.double.double_room_rate.ADDITIONAL_PEOPLE || 0)}</span>{renderSearchStatusIcon(String((currentRangeRates.double.double_room_rate.BASE_RATE || 0) + (currentRangeRates.double.double_room_rate.ADDITIONAL_PEOPLE || 0)))}</p>
                                                                                            </div>
                                                                                        )}
                                                                                    </div>
                                                                                )}
                                                                                {/* Placeholder for Triple and Quad if needed, following similar structure */}

                                                                                {/* Child Rate - Assuming only RATE is editable */}
                                                                                {currentRangeRates.child && currentRangeRates.child.child_rate && (
                                                                                    <div className="bg-white p-3 rounded shadow-sm">
                                                                                        <h5 onClick={() => handleItemClick('Child Rate')} className="font-medium mb-2 cursor-pointer hover:underline">Child Rate</h5>
                                                                                        {editingRates ? (
                                                                                            <div>
                                                                                                <label htmlFor={`childRate-${periodIndex}-${rangeIndex}`} className="block text-xs font-medium text-gray-600">Rate (R):</label>
                                                                                                <input type="number" id={`childRate-${periodIndex}-${rangeIndex}`} value={currentRangeRates.child.child_rate.RATE || ''} onChange={(e) => handleRateChange(periodIndex, rangeIndex, 'child', 'RATE', e.target.value)} className="mt-1 block w-full px-2 py-1 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="0"/>
                                                                                            </div>
                                                                                        ) : (
                                                                                            <p><strong onClick={() => handleItemClick('Rate')} className="cursor-pointer hover:underline">Rate:</strong> R<span onClick={() => handleItemClick(String(currentRangeRates.child.child_rate.RATE || ''))} className="cursor-pointer hover:underline">{currentRangeRates.child.child_rate.RATE !== null && currentRangeRates.child.child_rate.RATE !== '' ? currentRangeRates.child.child_rate.RATE : 'N/A'}</span>{renderSearchStatusIcon(String(currentRangeRates.child.child_rate.RATE || ''))}</p>
                                                                                        )}
                                                                                    </div>
                                                                                )}

                                                                                {/* Infant Rate - Assuming only RATE is editable */}
                                                                                {currentRangeRates.infant && currentRangeRates.infant.infant_rate && (
                                                                                    <div className="bg-white p-3 rounded shadow-sm">
                                                                                        <h5 onClick={() => handleItemClick('Infant Rate')} className="font-medium mb-2 cursor-pointer hover:underline">Infant Rate</h5>
                                                                                        {editingRates ? (
                                                                                            <div>
                                                                                                <label htmlFor={`infantRate-${periodIndex}-${rangeIndex}`} className="block text-xs font-medium text-gray-600">Rate (R):</label>
                                                                                                <input type="number" id={`infantRate-${periodIndex}-${rangeIndex}`} value={currentRangeRates.infant.infant_rate.RATE || ''} onChange={(e) => handleRateChange(periodIndex, rangeIndex, 'infant', 'RATE', e.target.value)} className="mt-1 block w-full px-2 py-1 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="0"/>
                                                                                            </div>
                                                                                        ) : (
                                                                                            <p><strong onClick={() => handleItemClick('Rate')} className="cursor-pointer hover:underline">Rate:</strong> R<span onClick={() => handleItemClick(String(currentRangeRates.infant.infant_rate.RATE || ''))} className="cursor-pointer hover:underline">{currentRangeRates.infant.infant_rate.RATE !== null && currentRangeRates.infant.infant_rate.RATE !== '' ? currentRangeRates.infant.infant_rate.RATE : 'N/A'}</span>{renderSearchStatusIcon(String(currentRangeRates.infant.infant_rate.RATE || ''))}</p>
                                                                                        )}
                                                                                    </div>
                                                                                )}
                                                                            </div>
                                                                        )}
                                                                    </div>
                                                                );
                                                            })}
                                                        </div>
                                                    ))}
                                                </div>
                                            ) : (
                                                <p className="text-gray-500">No rates available for editing or display.</p>
                                            )}
                                            {editingRates && ( // Save/Cancel for Rates at bottom of rates list as well
                                                <div className="flex justify-end gap-2 mt-4">
                                                    <button onClick={handleRatesCancel} className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-100">Cancel Rates</button>
                                                    <button onClick={handleRatesSave} className="px-4 py-2 text-sm bg-green-500 text-white rounded-lg hover:bg-green-600">Save Rates</button>
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            )}
                        </div>
                        {/* "Next/Finish" Button */}
                        {workflowState && roomTypeList.length > 0 && !loading && ( // Show button when not loading and workflow is active
                             <div className="mt-6">
                                 <button
                                     onClick={handleProceed}
                                     className="w-full px-4 py-3 bg-green-600 text-white font-semibold rounded-lg shadow-md hover:bg-green-700 disabled:bg-gray-400"
                                     disabled={loading} // Disable while loading analysis
                                 >
                                     {currentRoomTypeIndex < roomTypeList.length - 1
                                         ? `Next Room Type (${currentRoomTypeIndex + 2}/${roomTypeList.length})`
                                         : (workflowState.currentPropertyIndex < workflowState.totalProperties - 1
                                             ? "Finish Property & Next Property"
                                             : "Finish Section"
                                           )
                                     }
                                 </button>
                             </div>
                        )}
                         {workflowState && roomTypeList.length === 0 && !loading && ( // Handle case with no rooms for property
                            <div className="mt-6">
                                <button
                                    onClick={handleProceed}
                                    className="w-full px-4 py-3 bg-blue-600 text-white font-semibold rounded-lg shadow-md hover:bg-blue-700 disabled:bg-gray-400"
                                    disabled={loading}
                                >
                                    {workflowState.currentPropertyIndex < workflowState.totalProperties - 1
                                        ? "No Rooms Found - Proceed to Next Property"
                                        : "No Rooms Found - Finish Processing"
                                    }
                                </button>
                            </div>
                         )}
                    </div>
                    
                    {/* Right Column - PDF Viewer */}
                    <div className="lg:col-span-3 bg-white p-6 rounded-lg shadow-md sticky top-6 max-h-[calc(100vh-3rem)] overflow-y-auto"> {/* Make PDF viewer sticky */}
                        <h2 className="text-xl font-semibold mb-4">PDF Viewer</h2>
                        {pdfUrl ? <EnhancedPdfViewer url={pdfUrl} searchText={searchText} searchStatus={searchStatus} onSearchResult={handleSearchResult} /> : <p>No PDF loaded.</p>}
                    </div>
                </div>
            </div>
        </div>
    );

    function generateRows() {
        console.log("Room Type Analysis for CSV:", roomTypeAnalysis);
        console.log("Editable Room Rates for CSV:", editableRoomRates);

        const sanitizePeriodName = (name) => {
            if (!name) return "UnknownPeriod";
            return name.replace(/[^a-zA-Z0-9_]/g, '_');
        };

        // Use editableCancellationPolicies for CSV generation
        const cancellation_policies = editableCancellationPolicies || []; 
        const cancellation_policy_day_1 = cancellation_policies.length > 0 ? cancellation_policies[0] : { CANCELLATION_FEE: '', START_DAY: '' };
        
        // Use editableRoomCapacity for CSV generation
        const room_Capacity_Data = editableRoomCapacity || { MAX_ADULTS: '', MAX_CHILDREN: '', TOTAL_CAPACITY: '' };
        
        // Use editablePayStayOffers for CSV generation
        const pay_stay_offers = editablePayStayOffers || [];
        const pay_stay_offer_0 = pay_stay_offers.length > 0 ? pay_stay_offers[0] : { PAY_STAY_DAYS: '', PAY_STAY_FREE_NIGHTS: '' };
        
        const child_age_ranges = JSON.parse(localStorage.getItem('childAgeRanges') || "[]");
        
        // Use editableMealType for CSV generation
        const meal_type = editableMealType !== null ? editableMealType : ""; 

        const includes = (JSON.parse(localStorage.getItem('includes') || "[]")).map(item => item.trim()).join(', ');
        const excludes = (JSON.parse(localStorage.getItem('excludes') || "[]")).map(item => item.trim()).join(', ');

        // Common fields for all rows
        const commonFields = {
            "Property": selectedProperty || "",
            "Room type": selectedRoomType || "",
            "Includes": includes,
            "Excludes": excludes,
            "Meal basis": meal_type,
            "Child 1 From age": (child_age_ranges.length > 0 && child_age_ranges[0][0] !== undefined) ? child_age_ranges[0][0] : "",
            "Child 1 To age": (child_age_ranges.length > 0 && child_age_ranges[0][1] !== undefined) ? child_age_ranges[0][1] : "",
            "Max adults": room_Capacity_Data && room_Capacity_Data.MAX_ADULTS !== undefined ? room_Capacity_Data.MAX_ADULTS : "",
            "Max Children": room_Capacity_Data && room_Capacity_Data.MAX_CHILDREN !== undefined ? room_Capacity_Data.MAX_CHILDREN : "",
            "Max A+C": room_Capacity_Data && room_Capacity_Data.TOTAL_CAPACITY !== undefined ? room_Capacity_Data.TOTAL_CAPACITY : "",
            "Cancellation Policy from days 1": cancellation_policy_day_1 && cancellation_policy_day_1.START_DAY !== undefined ? cancellation_policy_day_1.START_DAY : "",
            "Cancellation fee % 1": cancellation_policy_day_1 && cancellation_policy_day_1.CANCELLATION_FEE !== undefined ? cancellation_policy_day_1.CANCELLATION_FEE : "",
            "Pay stay days": pay_stay_offer_0 && pay_stay_offer_0.PAY_STAY_DAYS !== undefined ? pay_stay_offer_0.PAY_STAY_DAYS : "",
            "Pay stay free nights": pay_stay_offer_0 && pay_stay_offer_0.PAY_STAY_FREE_NIGHTS !== undefined ? pay_stay_offer_0.PAY_STAY_FREE_NIGHTS : "",
            "Minimum Night Stay": editableMinNightStay !== null ? editableMinNightStay : "",
        };

        const rows = [];

        // Generate a row for each period and date range
        editableRoomRates.forEach((periodData) => {
            const sanitizedPeriod = sanitizePeriodName(periodData.period);
            
            periodData.dateRanges.forEach((dateRange, rangeIndex) => {
                const currentRangeRates = periodData.rates[rangeIndex];
                if (!currentRangeRates) return;

                const row = { ...commonFields };
                
                // Add date range information
                row["DATE_FROM"] = dateRange.startDate || "";
                row["DATE_TO"] = dateRange.endDate || "";

                // Add room rates for this period and date range
                const rateTypes = ['single', 'double', 'triple', 'quad'];
                rateTypes.forEach(rateType => {
                    const rateDetails = currentRangeRates[rateType];
                    if (rateDetails && rateDetails[`${rateType}_room_rate`]) {
                        const baseRate = rateDetails[`${rateType}_room_rate`].BASE_RATE;
                        const additionalPeople = rateDetails[`${rateType}_room_rate`].ADDITIONAL_PEOPLE;
                        
                        row[`${rateType.charAt(0).toUpperCase() + rateType.slice(1)} room Rate`] = baseRate !== undefined && baseRate !== null ? baseRate : "";
                        row[`${rateType.charAt(0).toUpperCase() + rateType.slice(1)} room Additional Rate`] = additionalPeople !== undefined && additionalPeople !== null ? additionalPeople : "";
                    }
                });

                const simpleRateTypes = ['child', 'infant'];
                simpleRateTypes.forEach(rateType => {
                    const rateDetails = currentRangeRates[rateType];
                    if (rateDetails && rateDetails[`${rateType}_rate`]) {
                        const rate = rateDetails[`${rateType}_rate`].RATE;
                        row[`${rateType.charAt(0).toUpperCase() + rateType.slice(1)} rate`] = rate !== undefined && rate !== null ? rate : "";
                    }
                });

                rows.push(row);
            });
        });
        
        console.log("Generated CSV rows:", rows);
        return rows;
    }
}

export default RoomTypeAnalysis; 